import React, { useMemo, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  IconButton,
  Box,
  InputAdornment,
  Alert,
  Button,
  Typography,
  Tooltip,
  FormHelperText,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteIcon from "@mui/icons-material/Delete";
import { useTranslation } from "react-i18next";
import { tokens } from "@/src/locales/tokens";
import DialogAddMediaLibrary from "../../media/dialog-add-media-library";
import { AddCircle, ContentCopy, ErrorOutline } from "@mui/icons-material";
import { FileType, MediaFile } from "@/src/constants/file-types";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { enqueueSnackbar } from "notistack";
import { CURRENCY_UNIT } from "@/src/constants/constant";

const VariantTable2 = ({
  variants,
  setVariants,
  existingFiles,
  setExistingFiles,
  errors,
  setErrors,
}) => {
  const { t } = useTranslation();
  const [openLibraryDialog, setOpenLibraryDialog] = useState(false);
  const [selectedVariantIndex, setSelectedVariantIndex] = useState(null);

  const validateVariant = (variant) => {
    const errors: any = {};

    if (variant.priceReal < variant.price) {
      errors.priceReal = "Giá niêm yết lớn hơn hoặc bằng giá bán";
      errors.price = "Giá bán nhỏ hơn hoặc bằng giá niêm yết";
    }
    if (variant.quantityPurchase > 1000) {
      errors.quantityPurchase = "Tối đa 1000";
    }

    return errors;
  };

  const handleInputChange = (index, field, value) => {
    const newVariants = [...variants];
    newVariants[index][field] = value;

    const priceReal = field === "priceReal" ? value : newVariants[index].priceReal;
    const price = field === "price" ? value : newVariants[index].price;

    const newErrors = { ...errors };

    if (field === "quantityPurchase") {
      if (value > 1000) {
        newErrors[index] = {
          ...newErrors[index],
          quantityPurchase: "Tối đa 1000",
        };
        setErrors(newErrors);
        return;
      } else {
        if (newErrors[index]) {
          delete newErrors[index].quantityPurchase;
          if (Object.keys(newErrors[index]).length === 0) {
            delete newErrors[index];
          }
        }
      }
    }

    if (priceReal < price) {
      newErrors[index] = {
        ...newErrors[index],
        priceReal: "Giá niêm yết lớn hơn hoặc bằng giá bán",
        price: "Giá bán nhỏ hơn hoặc bằng giá niêm yết",
      };
    } else {
      if (newErrors[index]) {
        delete newErrors[index].priceReal;
        delete newErrors[index].price;
        if (Object.keys(newErrors[index]).length === 0) {
          delete newErrors[index];
        }
      }
    }

    setVariants(newVariants);
    setErrors(newErrors);
  };

  const handleOpenLibraryDialog = (index) => {
    setSelectedVariantIndex(index);
    setOpenLibraryDialog(true);
  };

  const handleCloseLibrary = () => {
    setOpenLibraryDialog(false);
    setSelectedVariantIndex(null);
  };

  const imageComponent = (variant, index) => {
    if (variant.variantImage?.link) {
      return (
        <Box
          onClick={() => handleOpenLibraryDialog(index)}
          sx={{
            position: "relative",
            width: 60,
            height: 60,
            borderRadius: 1,
            overflow: "hidden",
            cursor: "pointer",
            "&:hover": {
              "& .change-image": {
                opacity: 1,
              },
            },
          }}
        >
          <Box
            component="img"
            src={variant.variantImage.link || "/assets/images/default-product.png"}
            onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
              e.currentTarget.src = "/assets/image_empty.png";
              e.currentTarget.classList.add("error-image");
            }}
            sx={{
              width: "100%",
              height: "100%",
              objectFit: "cover",
              "&.error-image": {
                objectFit: "contain",
                padding: 1,
                bgcolor: "background.neutral",
              },
            }}
          />
          <Box
            className="change-image"
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              bgcolor: "rgba(0,0,0,0.5)",
              opacity: 0,
              transition: "opacity 0.2s",
              borderRadius: 1,
            }}
          >
            <Typography
              variant="caption"
              sx={{
                color: "white",
                fontWeight: 500,
                px: 1,
                py: 0.5,
                borderRadius: 0.5,
                bgcolor: "rgba(0,0,0,0.25)",
                backdropFilter: "blur(2px)",
              }}
            >
              Thay đổi
            </Typography>
          </Box>
        </Box>
      );
    }

    return (
      <Button
        onClick={() => handleOpenLibraryDialog(index)}
        variant="outlined"
        sx={{
          borderStyle: "dashed",
          height: 60,
          width: 60,
          minWidth: 60,
          p: 0,
          borderRadius: 1,
          "&:hover": {
            borderStyle: "dashed",
            bgcolor: "primary.lighter",
          },
        }}
      >
        <AddCircle />
      </Button>
    );
  };

  const handleImageSelect = (mediaFiles: MediaFile[]) => {
    if (mediaFiles.length > 0) {
      const selectedImage = mediaFiles[0];

      const newVariants = [...variants];
      newVariants[selectedVariantIndex]["variantImage"] = selectedImage;
      setVariants(newVariants);
    }
    setOpenLibraryDialog(false);
  };

  const formatCurrency = (value) => {
    if (value === null || value === undefined || isNaN(value)) {
      return "";
    }
    return new Intl.NumberFormat("vi-VN", {
      style: "decimal",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const handleCopyRow = (sourceIndex) => {
    const sourceVariant = variants[sourceIndex];
    const fieldsToCopy = ["priceCapital", "price", "priceReal", "quantity", "quantityPurchase"];

    const updatedVariants = variants.map((variant, index) => {
      if (index === sourceIndex) return variant;
      const updatedVariant = { ...variant };
      fieldsToCopy.forEach((field) => {
        updatedVariant[field] = sourceVariant[field];
      });
      return updatedVariant;
    });

    const newErrors = {};
    updatedVariants.forEach((variant, index) => {
      const err = validateVariant(variant);
      if (Object.keys(err).length > 0) {
        newErrors[index] = err;
      }
    });

    setVariants(updatedVariants);
    setErrors(newErrors);

    enqueueSnackbar("Đã sao chép giá trị thành công", {
      variant: "success",
      autoHideDuration: 2000,
    });
  };

  return (
    <>
      <Alert severity="info" sx={{ mb: 2 }}>
        {t(tokens.contentManagement.product.variant.dialog.variantsCreated, {
          count: variants.length,
        })}
      </Alert>
      <TableContainer
        component={Paper}
        sx={{
          maxHeight: 400,
          overflow: "auto",
        }}
      >
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell align="center" sx={{ minWidth: 50 }}>
                STT
              </TableCell>
              <TableCell sx={{ minWidth: 100 }}>Hình ảnh</TableCell>
              <TableCell sx={{ minWidth: 200 }}>Thông số</TableCell>
              <TableCell sx={{ minWidth: 240 }}>Giá vốn</TableCell>
              <TableCell sx={{ minWidth: 240 }}>Giá niêm yết</TableCell>
              <TableCell sx={{ minWidth: 240 }}>Giá bán</TableCell>
              <TableCell sx={{ width: 150 }}>
                <Typography sx={{ display: "flex" }}>Tồn kho</Typography>
              </TableCell>
              <TableCell sx={{ width: 150 }}>
                <Typography sx={{ width: "100%", display: "flex" }}>
                  Tối đa
                  <Tooltip title="Nhập 0 nếu không giới hạn" placement="top">
                    <Typography sx={{ ml: 1 }}>
                      <ErrorOutline
                        sx={{
                          fontSize: 18,
                          cursor: "pointer",
                          mb: "3px",
                          color: "rgb(206, 166, 73)",
                        }}
                      />
                    </Typography>
                  </Tooltip>
                </Typography>
              </TableCell>
              <TableCell sx={{ width: 90 }}>Quản lý</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {Array.isArray(variants) &&
              variants.map((variant, index) => (
                <TableRow key={index} sx={{ pb: 3, height: "50px" }}>
                  <TableCell align="center" sx={{ padding: "8px" }}>
                    {index + 1}
                  </TableCell>
                  <TableCell sx={{ padding: "8px" }}>
                    {imageComponent(variant, index)}
                    <DialogAddMediaLibrary
                      open={openLibraryDialog}
                      onClose={handleCloseLibrary}
                      onSave={() => {}}
                      maxSelect={1}
                      existingFiles={existingFiles}
                      setExistingFiles={setExistingFiles}
                      onlyLib={true}
                      handleImageSelect={handleImageSelect}
                    />
                  </TableCell>
                  <TableCell sx={{ padding: "8px" }}>
                    <TruncatedText
                      typographyProps={{ fontSize: 15, fontWeight: 400 }}
                      text={
                        variant.variantNameOne
                          ? `${variant.variantNameOne}: ${variant.variantValueOne}`
                          : variant.variantValueOne
                      }
                      width="100%"
                    />
                    <TruncatedText
                      typographyProps={{ fontSize: 15, fontWeight: 400 }}
                      text={
                        variant.variantNameTwo
                          ? `${variant.variantNameTwo}: ${variant.variantValueTwo}`
                          : variant.variantValueTwo
                      }
                      width="100%"
                    />
                    <TruncatedText
                      typographyProps={{ fontSize: 15, fontWeight: 400 }}
                      text={
                        variant.variantNameThree
                          ? `${variant.variantNameThree}: ${variant.variantValueThree}`
                          : variant.variantValueThree
                      }
                      width="100%"
                    />
                  </TableCell>

                  <TableCell sx={{ padding: "8px" }}>
                    <TextField
                      type="text"
                      value={formatCurrency(variant.priceCapital)}
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/[^0-9]/g, "");
                        handleInputChange(index, "priceCapital", Number(rawValue));
                      }}
                      size="small"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">{CURRENCY_UNIT}</InputAdornment>
                        ),
                      }}
                    />
                  </TableCell>
                  <TableCell sx={{ position: "relative", padding: "8px" }}>
                    <TextField
                      type="text"
                      value={formatCurrency(variant.priceReal)}
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/[^0-9]/g, "");
                        handleInputChange(index, "priceReal", Number(rawValue));
                      }}
                      size="small"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">{CURRENCY_UNIT}</InputAdornment>
                        ),
                      }}
                    />
                    <FormHelperText
                      error={!!errors[index]?.priceReal}
                      sx={{
                        position: "absolute",
                        bottom: "-4px",
                        minHeight: "20px",
                        marginLeft: 0,
                      }}
                    >
                      {errors[index]?.priceReal || " "}
                    </FormHelperText>
                  </TableCell>
                  <TableCell sx={{ position: "relative", padding: "8px" }}>
                    <TextField
                      type="text"
                      value={formatCurrency(variant.price)}
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/[^0-9]/g, "");
                        handleInputChange(index, "price", Number(rawValue));
                      }}
                      size="small"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">{CURRENCY_UNIT}</InputAdornment>
                        ),
                      }}
                    />
                    <FormHelperText
                      error={!!errors[index]?.price}
                      sx={{
                        position: "absolute",
                        bottom: "-4px",
                        minHeight: "20px",
                        marginLeft: 0,
                      }}
                    >
                      {errors[index]?.price || " "}
                    </FormHelperText>
                  </TableCell>
                  <TableCell sx={{ padding: "8px" }}>
                    <TextField
                      type="text"
                      value={formatCurrency(variant.quantity)}
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/[^0-9]/g, "");
                        handleInputChange(index, "quantity", Number(rawValue));
                      }}
                      size="small"
                    />
                  </TableCell>
                  <TableCell sx={{ padding: "8px", position: "relative" }}>
                    <TextField
                      type="text"
                      value={formatCurrency(variant.quantityPurchase)}
                      onChange={(e) => {
                        const rawValue = e.target.value.replace(/[^0-9]/g, "");
                        handleInputChange(index, "quantityPurchase", Number(rawValue));
                      }}
                      size="small"
                    />
                    <FormHelperText
                      error={!!errors[index]?.quantityPurchase}
                      sx={{
                        position: "absolute",
                        bottom: "-4px",
                        minHeight: "20px",
                        marginLeft: 0,
                      }}
                    >
                      {errors[index]?.quantityPurchase || " "}
                    </FormHelperText>
                  </TableCell>
                  <TableCell sx={{ padding: "8px" }} align="center">
                    <Tooltip title="Sao chép giá trị của dòng này cho tất cả biến thể">
                      <IconButton
                        size="small"
                        onClick={() => handleCopyRow(index)}
                        sx={{
                          "&:hover": {
                            color: "primary.main",
                          },
                        }}
                      >
                        <ContentCopy fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
};

export default VariantTable2;
