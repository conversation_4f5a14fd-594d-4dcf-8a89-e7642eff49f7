import React, { useState, useEffect } from 'react';
import { Button, Space, Modal, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  CommonTable,
  useTablePagination,
  useTableSelection,
  useTableActions,
  createIndexColumn,
  createTextColumn,
  createNumberColumn,
  createDateColumn,
  createStatusColumn,
  createBooleanColumn,
} from '@/src/components/common/antd-table';

interface TestUser {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'inactive' | 'pending';
  isVip: boolean;
  orderCount: number;
  totalSpent: number;
  createdAt: string;
}

// Mock data
const generateMockData = (count: number): TestUser[] => {
  const statuses: ('active' | 'inactive' | 'pending')[] = ['active', 'inactive', 'pending'];
  const names = ['Nguyễn Văn A', 'Trần Thị B', '<PERSON><PERSON> Văn C', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'];
  
  return Array.from({ length: count }, (_, index) => ({
    id: `user-${index + 1}`,
    name: names[index % names.length] + ` ${index + 1}`,
    email: `user${index + 1}@example.com`,
    status: statuses[index % statuses.length],
    isVip: Math.random() > 0.7,
    orderCount: Math.floor(Math.random() * 50),
    totalSpent: Math.floor(Math.random() * 10000000),
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
  }));
};

const TestAntdTable = () => {
  const [users, setUsers] = useState<TestUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination
  const { pagination, setTotal } = useTablePagination({
    initialPageSize: 10,
    onChange: (page, pageSize) => {
      fetchUsers(page, pageSize);
    },
  });

  // Selection
  const { rowSelection, selectedRows, clearSelection } = useTableSelection<TestUser>({
    rowKey: 'id',
    getCheckboxProps: (record) => ({
      disabled: record.status === 'inactive',
    }),
  });

  // Actions
  const actions = useTableActions<TestUser>({
    onEdit: handleEdit,
    onDelete: handleDelete,
    canEdit: () => true,
    canDelete: (record) => record.status !== 'active',
  });

  // Columns
  const columns = [
    createIndexColumn('STT', { width: 60 }),
    createTextColumn<TestUser>('name', 'Tên', 'name', {
      width: 150,
      ellipsis: true,
      maxLength: 20,
      tooltip: true,
    }),
    createTextColumn<TestUser>('email', 'Email', 'email', {
      width: 200,
      ellipsis: true,
      copyable: true,
    }),
    createStatusColumn<TestUser>('status', 'Trạng thái', 'status', {
      width: 120,
      statusMap: {
        'active': { color: 'success', text: 'Hoạt động' },
        'inactive': { color: 'default', text: 'Không hoạt động' },
        'pending': { color: 'warning', text: 'Chờ duyệt' },
      },
    }),
    createBooleanColumn<TestUser>('isVip', 'VIP', 'isVip', {
      width: 80,
      trueText: 'VIP',
      falseText: 'Thường',
      trueColor: 'gold',
      falseColor: 'default',
    }),
    createNumberColumn<TestUser>('orderCount', 'Số đơn hàng', 'orderCount', {
      width: 120,
    }),
    createNumberColumn<TestUser>('totalSpent', 'Tổng chi tiêu', 'totalSpent', {
      width: 150,
      format: 'currency',
    }),
    createDateColumn<TestUser>('createdAt', 'Ngày tạo', 'createdAt', {
      width: 120,
      format: 'DD/MM/YYYY',
    }),
  ];

  // Mock API calls
  const fetchUsers = async (page = 1, pageSize = 10) => {
    setLoading(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    try {
      const allData = generateMockData(100);
      
      // Filter by search term
      const filteredData = searchTerm
        ? allData.filter(user => 
            user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.email.toLowerCase().includes(searchTerm.toLowerCase())
          )
        : allData;
      
      // Paginate
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedData = filteredData.slice(startIndex, endIndex);
      
      setUsers(paginatedData);
      setTotal(filteredData.length);
    } catch (error) {
      message.error('Lỗi khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  function handleEdit(user: TestUser) {
    message.info(`Chỉnh sửa người dùng: ${user.name}`);
  }

  function handleDelete(user: TestUser) {
    Modal.confirm({
      title: 'Xác nhận xóa',
      content: `Bạn có chắc chắn muốn xóa người dùng "${user.name}" không?`,
      okText: 'Xóa',
      cancelText: 'Hủy',
      okType: 'danger',
      onOk: async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 500));
          message.success('Xóa thành công');
          fetchUsers();
        } catch (error) {
          message.error('Xóa thất bại');
        }
      },
    });
  }

  const handleDeleteMultiple = () => {
    if (selectedRows.length === 0) return;
    
    Modal.confirm({
      title: 'Xác nhận xóa nhiều người dùng',
      content: `Bạn có chắc chắn muốn xóa ${selectedRows.length} người dùng đã chọn không?`,
      okText: 'Xóa',
      cancelText: 'Hủy',
      okType: 'danger',
      onOk: async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 1000));
          message.success(`Xóa ${selectedRows.length} người dùng thành công`);
          clearSelection();
          fetchUsers();
        } catch (error) {
          message.error('Xóa thất bại');
        }
      },
    });
  };

  const handleAddUser = () => {
    message.info('Chức năng thêm người dùng');
  };

  useEffect(() => {
    fetchUsers();
  }, [searchTerm]);

  return (
    <div style={{ padding: 24 }}>
      <h1>Test Ant Design Table Component</h1>
      
      <CommonTable
        columns={columns}
        data={users}
        loading={loading}
        pagination={pagination}
        rowSelection={rowSelection}
        actions={actions}
        searchable
        searchPlaceholder="Tìm kiếm theo tên hoặc email..."
        onSearch={setSearchTerm}
        toolbar={{
          title: 'Quản lý người dùng',
          extra: (
            <Space>
              {selectedRows.length > 0 && (
                <Button danger onClick={handleDeleteMultiple}>
                  Xóa đã chọn ({selectedRows.length})
                </Button>
              )}
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddUser}>
                Thêm người dùng
              </Button>
            </Space>
          ),
          showRefresh: true,
          onRefresh: () => fetchUsers(),
        }}
        emptyText="Không có người dùng nào"
        emptyDescription="Hệ thống chưa có người dùng nào được tạo"
        emptyAction={{
          text: 'Thêm người dùng đầu tiên',
          onClick: handleAddUser,
        }}
        rowKey="id"
        size="middle"
        bordered
        scroll={{ x: 1200 }}
      />
    </div>
  );
};

export default TestAntdTable;
