import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Box,
  Button,
  Container,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  ToggleButton,
  ToggleButtonGroup,
  Chip,
  Stack,
  TablePagination,
  Tooltip,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";

import DownloadIcon from "@mui/icons-material/Download";
import {
  LocalizationProvider,
  DateRangePicker,
  DatePicker,
  DateRange,
} from "@mui/x-date-pickers-pro";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { useAffiliation } from "@/src/api/hooks/affiliation/use-affiliation";
import { useStoreId } from "@/src/hooks/use-store-id";
import { GetCommissionOrderRequest } from "@/src/api/types/affiliation.type";
import _ from "lodash";
import { useRouter } from "next/router";
import dayjs from "dayjs";
import { ListItemHover, ListItemsOrderTable } from "@/src/components/orders/draft/TableOrder";
import { formatPrice } from "@/src/api/types/membership.types";
import useSnackbar from "@/src/hooks/use-snackbar";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { CURRENCY_UNIT } from "@/src/constants/constant";

const OrderManagement = () => {
  const { getCommissionsConfig, getCommissionOrder, exportExcelCommissionOrder } = useAffiliation();
  const storeId = useStoreId();
  const snackbar = useSnackbar();

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedTab, setSelectedTab] = useState("today");
  const [searchQuery, setSearchQuery] = useState("");
  const [chartDateRange, setChartDateRange] = useState<any>([null, null]);
  const [open, setOpen] = useState(false);
  const [totalCount, setTotalCount] = useState<number>();
  const [commissionOrders, setCommissionOrders] = useState<any[]>([]);
  const [filterData, setFilterData] = useState<GetCommissionOrderRequest>();

  // const fetchCommissionOrder = async () => {
  //   const res = await getCommissionOrder(data)
  //   console.log({ res })
  // }
  // useEffect(() => {
  //   if (storeId) {
  //     fetchCommissionOrder()
  //   }
  // }, [storeId])
  const fetchCommissionsConfig = async (shopId) => {
    const response = await getCommissionsConfig({ shopId });
    if (response?.data) {
      const { commissionsConfigId, shopId, isActive, ...filteredData } = response.data.data;
      if (commissionsConfigId) {
        if (isActive === true) {
          // snackbar.success("Chính sách hoa hồng đã được bật")
        } else {
          snackbar.warning("Hãy kích hoạt chính sách hoa hồng");
        }
      } else {
        snackbar.warning("Hãy tạo chính sách hoa hồng");
      }
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchCommissionsConfig(storeId);
    }
  }, [storeId]);

  const fetchCommissionOrder = async (currentPage, pageSize, data: GetCommissionOrderRequest) => {
    try {
      data.PageNumber = currentPage + 1;
      data.PageSize = pageSize;

      const response = await getCommissionOrder(data);
      if (response && response.data) {
        setCommissionOrders(response?.data?.data);
        setTotalCount(response.data.total || 0);
      }
    } catch (error) {}
  };

  const debouncedFetchCommissionOrder = useCallback(
    _.debounce((currentPage, pageSize, data) => {
      fetchCommissionOrder(currentPage, pageSize, data);
    }, 400), // Delay 400ms
    []
  );

  useEffect(() => {
    if (filterData?.shopId) {
      debouncedFetchCommissionOrder(page, rowsPerPage, filterData);
    }

    return () => {
      debouncedFetchCommissionOrder.cancel();
    };
  }, [page, rowsPerPage, debouncedFetchCommissionOrder, filterData]);

  useEffect(() => {
    if (storeId) {
      const startOfDay = dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss");
      const endOfDay = dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss");

      setFilterData((prevState) => ({
        ...prevState,
        shopId: storeId,
        StartDate: startOfDay,
        EndDate: endOfDay,
      }));
    }
  }, [storeId]);

  const handleExportExcel = async () => {
    try {
      const response = await exportExcelCommissionOrder(filterData);

      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "DanhSachDonHang.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Export Excel failed:", error);
    }
  };
  const handleChartStartDateChange = (newValue) => {
    if (newValue.toString() !== "Invalid Date") {
      const endDate = chartDateRange[1];
      const startD = newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss");
      let updatedRange;

      if (!endDate || newValue.isAfter(endDate)) {
        updatedRange = [newValue, newValue];
        setFilterData((prev) => ({
          ...prev,
          StartDate: startD,
          EndDate: newValue.endOf("day").format("YYYY-MM-DD HH:mm:ss"),
        }));
      } else {
        const endD = endDate.endOf("day").format("YYYY-MM-DD HH:mm:ss");
        updatedRange = [newValue, endDate];
        setFilterData((prev) => ({ ...prev, StartDate: startD, EndDate: endD }));
      }
      setChartDateRange(updatedRange);
    }
  };

  const handleChartEndDateChange = (newValue) => {
    const validDayjs = dayjs(newValue);

    if (validDayjs.isValid() && validDayjs.year() >= 1000) {
      const startDate = chartDateRange[0];
      const endD = newValue.endOf("day").format("YYYY-MM-DD HH:mm:ss");
      let updatedRange;

      if (!startDate || newValue.isBefore(startDate, "day")) {
        updatedRange = [newValue, newValue];
        setFilterData((prev) => ({
          ...prev,
          StartDate: newValue.startOf("day").format("YYYY-MM-DD HH:mm:ss"),
          EndDate: endD,
        }));
      } else {
        const startD = startDate.startOf("day").format("YYYY-MM-DD HH:mm:ss");
        updatedRange = [startDate, newValue];
        setFilterData((prev) => ({ ...prev, StartDate: startD, EndDate: endD }));
      }
      setChartDateRange(updatedRange);
    }
  };

  return (
    <Container
      maxWidth={false}
      sx={{
        boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.1)",
        borderRadius: "15px",
        padding: "20px",
        margin: "20px 0",
        background: "#fff",
        width: "100%",
      }}
    >
      <Typography sx={{ fontSize: "20px", fontWeight: 700, marginBottom: 1.5 }}>
        Quản lý đơn hàng
      </Typography>

      <Box sx={{ mb: 2 }}>
        <ToggleButtonGroup
          value={selectedTab}
          exclusive
          onChange={(e, newTab) => {
            if (newTab !== null) {
              setSelectedTab(newTab);
              setPage(0);
            }
          }}
          sx={{
            margin: "0",
            padding: "0",
            border: "1px solid #ccc",
            borderRadius: "10px",
            overflow: "hidden",
            width: { xs: "100%", sm: "auto" },
          }}
        >
          <ToggleButton
            value="today"
            sx={{
              height: { xs: 40, sm: 30 },
              width: { xs: "33.33%", sm: "120px" },
              padding: { xs: "8px 4px", sm: "15px 2px" },
              fontSize: { xs: 13, sm: 14 },
              boxShadow: "none",
              border: "none",
              fontWeight: "400",
              color: selectedTab === "today" ? "#fff" : "#000",
              backgroundColor: selectedTab === "today" ? "#2654FE" : "#fff",
              borderRadius: "0",
              textTransform: "none",
              "&.Mui-selected": {
                backgroundColor: "#2654FE",
                color: "#fff",
                fontWeight: "400",
                "&:hover": {
                  backgroundColor: "#1E45D9",
                },
              },
            }}
          >
            Hôm nay
          </ToggleButton>

          <ToggleButton
            value="7days"
            sx={{
              height: { xs: 40, sm: 30 },
              width: { xs: "33.33%", sm: "120px" },
              padding: { xs: "8px 4px", sm: "15px 15px" },
              fontSize: { xs: 13, sm: 14 },
              boxShadow: "none",
              border: "none",
              fontWeight: "400",
              color: selectedTab === "7days" ? "#fff" : "#000",
              backgroundColor: selectedTab === "7days" ? "#2654FE" : "#fff",
              borderRadius: "0",
              textTransform: "none",
              "&.Mui-selected": {
                backgroundColor: "#2654FE",
                color: "#fff",
                fontWeight: "400",
                "&:hover": {
                  backgroundColor: "#1E45D9",
                },
              },
            }}
          >
            7 ngày
          </ToggleButton>

          <ToggleButton
            value="30days"
            sx={{
              height: { xs: 40, sm: 30 },
              width: { xs: "33.33%", sm: "120px" },
              padding: { xs: "8px 4px", sm: "15px 15px" },
              fontSize: { xs: 13, sm: 14 },
              boxShadow: "none",
              border: "none",
              fontWeight: "400",
              color: selectedTab === "30days" ? "#fff" : "#000",
              backgroundColor: selectedTab === "30days" ? "#2654FE" : "#fff",
              borderRadius: "0",
              textTransform: "none",
              "&.Mui-selected": {
                backgroundColor: "#2654FE",
                color: "#fff",
                fontWeight: "400",
                "&:hover": {
                  backgroundColor: "#1E45D9",
                },
              },
            }}
          >
            30 ngày
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      <Stack
        direction={{ xs: "column", sm: "row" }}
        justifyContent="space-between"
        alignItems={{ xs: "stretch", sm: "center" }}
        gap={2}
        sx={{ width: "100%", mb: 2 }}
      >
        <Stack
          direction={{ xs: "column", sm: "row" }}
          alignItems={{ xs: "stretch", sm: "center" }}
          gap={1}
          sx={{ flex: 1 }}
        >
          <TextField
            variant="outlined"
            placeholder="Tìm tên, mã đối tác"
            onChange={(e) =>
              setFilterData((prevState) => {
                return { ...prevState, SearchTerm: e.target.value };
              })
            }
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              width: { xs: "100%", sm: 280 },
              "& .MuiOutlinedInput-root": {
                borderRadius: "8px",
                height: 36,
                fontSize: 14,
              },
            }}
          />

          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              gap: 1,
              width: { xs: "100%", sm: "auto" },
            }}
          >
            <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
              <DatePicker
                value={chartDateRange[0]}
                format="DD/MM/YYYY"
                onChange={handleChartStartDateChange}
                slotProps={{
                  textField: {
                    size: "small",
                    sx: {
                      width: { xs: "100%", sm: 200 },
                      flex: { xs: 1, sm: "none" },
                      "& .MuiOutlinedInput-root": {
                        height: 36,
                        borderRadius: "8px",
                        fontSize: 14,
                      },
                    },
                  },
                }}
              />
              <DatePicker
                value={chartDateRange[1]}
                format="DD/MM/YYYY"
                onChange={handleChartEndDateChange}
                shouldDisableDate={(date) => date.isBefore(chartDateRange[0], "day")}
                slotProps={{
                  textField: {
                    size: "small",
                    sx: {
                      width: { xs: "100%", sm: 200 },
                      flex: { xs: 1, sm: "none" },
                      "& .MuiOutlinedInput-root": {
                        height: 36,
                        borderRadius: "8px",
                        fontSize: 14,
                      },
                    },
                  },
                }}
              />
            </LocalizationProvider>
          </Box>
        </Stack>

        <Button
          variant="outlined"
          startIcon={<DownloadIcon />}
          onClick={handleExportExcel}
          size="small"
          sx={{
            textTransform: "none",
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#2654FE",
            border: "1.5px solid #2654FE",
            fontSize: "14px",
            fontWeight: 600,
            minWidth: 60,
            width: { xs: "calc(50% - 2px)", sm: 150 },
            height: 36,
            boxShadow: "none",
            transition: "all 0.2s",
            "&:hover": {
              backgroundColor: "#F0F6FF",
              borderColor: "#2654FE",
              color: "#2654FE",
            },
            "& .MuiButton-startIcon": {
              marginRight: 1,
            },
          }}
        >
          Export
        </Button>
      </Stack>

      <Box sx={{ width: "100%", overflowX: "auto" }}>
        <Table sx={{ minWidth: 1000 }} size="small">
          <TableHead>
            <TableRow>
              {[
                { header: "STT", width: "60px" },
                { header: "Mã đơn hàng", width: "120px" },
                { header: "Sản phẩm", width: "180px" },
                { header: "Ngày", width: "100px" },
                { header: "Khách hàng", width: "150px" },
                { header: "Doanh thu", width: "120px" },
                { header: "Tự giới thiệu", width: "120px" },
                { header: "HH tự giới thiệu", width: "140px" },
                { header: "Bậc 1", width: "80px" },
                { header: "Hoa hồng bậc 1", width: "140px" },
                { header: "Bậc 2", width: "80px" },
                { header: "Hoa hồng bậc 2", width: "140px" },
              ].map((item) => (
                <TableCell
                  key={item.header}
                  sx={{
                    fontWeight: "bold",
                    minWidth: item.width,
                  }}
                >
                  {item.header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {commissionOrders.length > 0 &&
              commissionOrders.map((order, index) => (
                <TableRow key={order.orderId}>
                  <TableCell>{page * rowsPerPage + index + 1}</TableCell>
                  <TableCell>{order.orderNo}</TableCell>
                  <TableCell sx={{ display: "flex", alignItems: "center", gap: "5px" }}>
                    <Tooltip
                      title={
                        <Box p={1}>
                          <ListItemHover listItems={order?.listItems} />
                        </Box>
                      }
                    >
                      <Box>
                        <ListItemsOrderTable listItems={order?.listItems} />
                      </Box>
                    </Tooltip>
                  </TableCell>
                  <TableCell>{dayjs(order.created).format("DD/MM/YYYY")}</TableCell>
                  <TableCell>{order.creator?.fullName}</TableCell>
                  <TableCell>
                    {formatPrice(order.price)}
                    {CURRENCY_UNIT}
                  </TableCell>
                  {order?.commissionBreakUp?.commissionDistribution.length > 0 &&
                    order?.commissionBreakUp?.commissionDistribution.map((item) => (
                      <>
                        <TableCell>{item?.fullName}</TableCell>
                        <TableCell>
                          {formatPrice(item?.value)}
                          {CURRENCY_UNIT}
                        </TableCell>
                      </>
                    ))}
                </TableRow>
              ))}
          </TableBody>
        </Table>

        <Box display="flex" justifyContent="flex-end" mt={2} pr={2}>
          <TablePagination
            labelRowsPerPage="Số hàng mỗi trang"
            rowsPerPageOptions={rowPerPageOptionsDefault}
            component="div"
            count={totalCount || 0}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={(event, newPage) => setPage(newPage)}
            onRowsPerPageChange={(event) => {
              setRowsPerPage(parseInt(event.target.value, 10));
              setPage(1);
            }}
          />
        </Box>
      </Box>
    </Container>
  );
};

export default OrderManagement;
