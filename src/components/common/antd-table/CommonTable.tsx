import React, { forwardRef, useImperativeHandle, useMemo, useState } from "react";
import {
  Table,
  Card,
  Space,
  Button,
  Input,
  Tooltip,
  Dropdown,
  Empty,
  Typography,
  Flex,
  MenuProps,
} from "antd";
import { RefreshIcon, MoreIcon } from "./utils/iconHelpers";
import { CommonTableProps, TableRef, TableAction } from "./types";

const { Search } = Input;
const { Title } = Typography;

const CommonTable = forwardRef<TableRef, CommonTableProps>((props, ref) => {
  const {
    columns,
    data,
    loading = false,
    pagination,
    rowSelection,
    actions,
    actionsColumnProps,
    searchable = false,
    searchPlaceholder = "Tìm kiếm...",
    onSearch,
    toolbar,
    emptyText = "Không có dữ liệu",
    emptyDescription,
    emptyAction,
    bordered = true,
    size = "middle",
    sticky = false,
    scroll,
    rowKey = "id",
    expandable,
    className,
    tableClassName,
    onRow,
    onChange,
    ...restProps
  } = props;

  const [searchValue, setSearchValue] = useState("");

  // Expose methods via ref
  useImperativeHandle(ref, () => ({
    refresh: () => {
      toolbar?.onRefresh?.();
    },
    getSelectedRows: () => {
      return data.filter((item) =>
        rowSelection?.selectedRowKeys?.includes(
          typeof rowKey === "function" ? rowKey(item) : item[rowKey as string]
        )
      );
    },
    clearSelection: () => {
      rowSelection?.onChange?.([], []);
    },
  }));

  // Create actions column
  const actionsColumn = useMemo(() => {
    if (!actions || actions.length === 0) return null;

    return {
      key: "actions",
      title: actionsColumnProps?.title || "Thao tác",
      width: actionsColumnProps?.width || 120,
      fixed: actionsColumnProps?.fixed,
      align: "center" as const,
      render: (_: any, record: any) => {
        const visibleActions = actions.filter((action) =>
          action.visible ? action.visible(record) : true
        );

        if (visibleActions.length === 0) return null;

        if (visibleActions.length <= 2) {
          return (
            <Space size="small">
              {visibleActions.map((action) => (
                <Tooltip key={action.key} title={action.tooltip || action.label}>
                  <Button
                    type={action.type === "danger" ? "primary" : "default"}
                    danger={action.type === "danger"}
                    size="small"
                    icon={action.icon}
                    onClick={() => action.onClick(record)}
                    disabled={action.disabled ? action.disabled(record) : false}
                  >
                    {!action.icon && action.label}
                  </Button>
                </Tooltip>
              ))}
            </Space>
          );
        }

        // If more than 2 actions, use dropdown
        const menuItems: MenuProps["items"] = visibleActions.map((action) => ({
          key: action.key,
          label: action.label,
          icon: action.icon,
          disabled: action.disabled ? action.disabled(record) : false,
          danger: action.type === "danger",
          onClick: () => action.onClick(record),
        }));

        return (
          <Dropdown menu={{ items: menuItems }} trigger={["click"]}>
            <Button size="small" icon={<MoreIcon />} />
          </Dropdown>
        );
      },
    };
  }, [actions, actionsColumnProps]);

  // Combine columns with actions
  const finalColumns = useMemo(() => {
    const cols = [...columns];
    if (actionsColumn) {
      cols.push(actionsColumn);
    }
    return cols;
  }, [columns, actionsColumn]);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchValue(value);
    onSearch?.(value);
  };

  // Custom empty state
  const emptyState = useMemo(() => {
    if (!emptyDescription && !emptyAction) {
      return <Empty description={emptyText} />;
    }

    return (
      <Empty
        description={
          <div>
            <Typography.Text type="secondary">{emptyText}</Typography.Text>
            {emptyDescription && (
              <div style={{ marginTop: 8 }}>
                <Typography.Text type="secondary" style={{ fontSize: 12 }}>
                  {emptyDescription}
                </Typography.Text>
              </div>
            )}
          </div>
        }
      >
        {emptyAction && (
          <Button type="primary" onClick={emptyAction.onClick}>
            {emptyAction.text}
          </Button>
        )}
      </Empty>
    );
  }, [emptyText, emptyDescription, emptyAction]);

  // Default pagination config
  const defaultPagination = useMemo(() => {
    if (pagination === false) return false;

    return {
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: [number, number]) =>
        `${range[0]}-${range[1]} của ${total} mục`,
      pageSizeOptions: ["10", "20", "50", "100"],
      ...pagination,
    };
  }, [pagination]);

  return (
    <div className={className}>
      {/* Toolbar */}
      {(toolbar || searchable) && (
        <Card size="small" style={{ marginBottom: 16 }}>
          <Flex justify="space-between" align="center" wrap="wrap" gap={16}>
            <div>
              {toolbar?.title && (
                <Title level={4} style={{ margin: 0 }}>
                  {toolbar.title}
                </Title>
              )}
            </div>

            <Space wrap>
              {searchable && (
                <Search
                  placeholder={searchPlaceholder}
                  allowClear
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  onSearch={handleSearch}
                  style={{ width: 250 }}
                />
              )}

              {toolbar?.showRefresh && (
                <Tooltip title="Làm mới">
                  <Button icon={<RefreshIcon />} onClick={toolbar.onRefresh} loading={loading} />
                </Tooltip>
              )}

              {toolbar?.extra}
            </Space>
          </Flex>
        </Card>
      )}

      {/* Table */}
      <Card className={tableClassName}>
        <Table
          columns={finalColumns}
          dataSource={data}
          loading={loading}
          pagination={defaultPagination}
          rowSelection={rowSelection}
          bordered={bordered}
          size={size}
          sticky={sticky}
          scroll={scroll}
          rowKey={rowKey}
          expandable={expandable}
          onRow={onRow}
          onChange={onChange}
          locale={{
            emptyText: emptyState,
          }}
          {...restProps}
        />
      </Card>
    </div>
  );
});

CommonTable.displayName = "CommonTable";

export default CommonTable;
