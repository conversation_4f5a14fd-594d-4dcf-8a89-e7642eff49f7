import { customersData } from "@/src/_mock/customers-data";
import { Delete, EditNotifications } from "@mui/icons-material";
import {
  Box,
  Button,
  Checkbox,
  Chip,
  Icon,
  IconButton,
  SxProps,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TablePagination,
  TableRow,
  Theme,
  Tooltip,
  Typography,
} from "@mui/material";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import React, { useCallback, useEffect, useState } from "react";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import { formatMoney } from "@/src/utils/format-money";
import { useUser } from "@/src/api/hooks/user/use-user";
import _ from "lodash";
import { useStoreId } from "@/src/hooks/use-store-id";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { CURRENCY_UNIT, PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { usePathname } from "next/navigation";
import VisibilityIcon from "@mui/icons-material/Visibility";
import TitleDialog from "../dialog/TitleDialog";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useTranslation } from "react-i18next";
import TruncatedText from "../truncated-text/truncated-text";
import dayjs from "dayjs";
import { formatTruncatedText } from "@/src/utils/format";

const getStatusProps = (status: string) => {
  const isActive = status === "Actived";
  return {
    text: isActive ? "Kích hoạt" : "Vô hiệu hóa",
    sx: {
      backgroundColor: isActive ? `#00BF50` : "red",
      color: "#00BF50",
      padding: "4px 8px",
      opacity: 0.3,
      borderRadius: "4px",
      textAlign: "center",
      fontWeight: "medium",
    } as SxProps<Theme>,
  };
};

const VOUCHER_STATUS = {
  ACTIVED: "Actived",
  INACTIVED: "Inactived",
} as const;

const VOUCHER_STATUS_LABEL: Record<string, string> = {
  [VOUCHER_STATUS.ACTIVED]: "Kích hoạt",
  [VOUCHER_STATUS.INACTIVED]: "Vô hiệu hóa",
};

interface StatusBadgeProps {
  status: string;
}

const StatusBadge = ({ status }: StatusBadgeProps) => {
  const getStatusColor = (status: string): SxProps<Theme> => {
    switch (status) {
      case VOUCHER_STATUS.ACTIVED:
        return {
          bgcolor: "#E8F5E9", // Màu nền xanh nhạt
          color: "#2E7D32", // Màu chữ xanh đậm
          border: "1px solid #A5D6A7", // Viền xanh
        };
      case VOUCHER_STATUS.INACTIVED:
        return {
          bgcolor: "#FFEBEE", // Màu nền đỏ nhạt
          color: "#C62828", // Màu chữ đỏ đậm
          border: "1px solid #EF9A9A", // Viền đỏ
        };
      default:
        return {
          bgcolor: "rgb(88, 174, 255)", // Màu nền mặc định (xanh dương)
          color: "rgb(255, 255, 255)", // Màu chữ trắng
          border: "1px solid rgb(0, 132, 255)", // Viền xanh dương
        };
    }
  };

  const statusStyle = getStatusColor(status);

  return (
    <Chip
      label={VOUCHER_STATUS_LABEL[status] || status}
      size="small"
      sx={{
        ...statusStyle,
        fontWeight: 500,
        fontSize: "0.75rem",
        height: "24px",
      }}
    />
  );
};
export default function TableCustomer({
  users,
  page,
  setPage,
  rowsPerPage,
  setRowsPerPage,
  totalCount,
  searchText,
  isGranted,
  fetchUserList,
}) {
  const { listUser, loading, deleteUsers } = useUser();
  const storeId = useStoreId();
  const [selected, setSelected] = useState([]);
  const pathname = usePathname();
  const router = useRouter();
  const [confirmDialog, setConfirmDialog] = useState<{ open: boolean; customer: any }>({
    open: false,
    customer: null,
  });
  const snackbar = useSnackbar();
  const { t } = useTranslation();

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      setSelected(users.map((c) => c.userId));
    } else {
      setSelected([]);
    }
  };
  const handleSelectOne = (event, id) => {
    if (event.target.checked) {
      setSelected((prevSelected) => [...prevSelected, id]);
    } else {
      setSelected((prevSelected) => prevSelected.filter((item) => item !== id));
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleEdit = (cId) => {
    router.push(`${paths.customers.detail}?id=${cId}`);
  };

  const handleCloseConfirmDialog = () => {
    setConfirmDialog({ open: false, customer: null });
  };

  const handleConfirmDelete = async () => {
    const userIds = [confirmDialog?.customer?.userId];
    if (storeId) {
      const response = await deleteUsers(storeId, userIds);
      if (response && response.status === 200) {
        await router.push(paths.customers.list);
        snackbar.success(t("user.deleteSuccess"));
        fetchUserList(page, rowsPerPage, searchText, storeId);
        handleCloseConfirmDialog();
      }
    }
  };

  return (
    <>
      <Box sx={{ width: "100%", overflowX: "auto" }}>
        <Table>
          <TableHead>
            <TableRow>
              {/* <TableCell padding="checkbox">
                <Checkbox
                  checked={users.length > 0 && selected.length === users.length}
                  onChange={handleSelectAll}
                />
              </TableCell> */}
              <TableCell align="center">STT</TableCell>
              <TableCell sx={{ minWidth: 150 }}>Mã khách hàng</TableCell>
              <TableCell sx={{ width: 250 }}>Tên khách hàng</TableCell>
              <TableCell sx={{ minWidth: 100 }}>Số điện thoại</TableCell>
              <TableCell sx={{ minWidth: 100 }}>Trạng thái</TableCell>
              <TableCell sx={{ minWidth: 100 }}>Phân loại</TableCell>
              <TableCell sx={{ width: 200 }}>Hạng thành viên</TableCell>
              <TableCell align="right" sx={{ minWidth: 150 }}>
                Đã chi tiêu ({CURRENCY_UNIT})
              </TableCell>
              <TableCell sx={{ minWidth: 200 }}>Thời gian kích hoạt</TableCell>
              <TableCell
                sx={{
                  minWidth: 100,
                  position: "sticky",
                  right: 0,
                  bottom: 0,
                  backgroundColor: "#fff",
                  zIndex: 3,
                  boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                  padding: { xs: "16px 4px", sm: "20px 16px" },
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  width: { xs: "70px", sm: "90px" },
                }}
              >
                Quản lý
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.length > 0 ? (
              users.map((c, index) => (
                <TableRow key={c.userId}>
                  {/* <TableCell padding="checkbox">
                    <Checkbox
                      checked={selected.includes(c.userId)}
                      onChange={(event) => handleSelectOne(event, c.userId)}
                    />
                  </TableCell> */}
                  <TableCell align="center">{page * rowsPerPage + index + 1}</TableCell>
                  <TableCell>{c.referralCode}</TableCell>
                  <TableCell
                    color="text.primary"
                    sx={{
                      width: 250,
                    }}
                  >
                    <TruncatedText
                      {...formatTruncatedText({
                        text: c.fullname,
                        isGranted: isGranted(pathname, PERMISSION_TYPE_ENUM.Edit),
                        openInNewTab: false,
                        actionNeed: () => handleEdit(c.userId),
                        width: "250px",
                      })}
                    />
                  </TableCell>
                  <TableCell>{c.phoneNumber}</TableCell>
                  <TableCell>
                    <StatusBadge status={c.status} />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: "flex", flexWrap: "nowrap", overflow: "hidden" }}>
                      {c.tags?.slice(0, 2).map((tag, index) => (
                        <Chip key={index} label={tag} sx={{ mr: 1, flexShrink: 0 }} />
                      ))}
                      {c.tags?.length > 2 && (
                        <Tooltip title={c.tags.join(", ")} arrow>
                          <Chip label="..." sx={{ flexShrink: 0, cursor: "pointer" }} />
                        </Tooltip>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <TruncatedText text={c.membershipLevel?.levelName} />
                  </TableCell>
                  <TableCell align="right">
                    {formatMoney(c.totalSpent)}
                    {CURRENCY_UNIT}
                  </TableCell>
                  <TableCell>{dayjs(c.created).format("DD/MM/YYYY HH:mm:ss")}</TableCell>
                  <TableCell
                    sx={{
                      display: "flex",
                      flexDirection: "row",
                      alignItems: "start",
                      position: "sticky",
                      right: 0,
                      backgroundColor: "#fff",
                      zIndex: 2,
                      padding: "15px 16px",
                      boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                    }}
                  >
                    <IconButton size="small" color="primary" onClick={() => handleEdit(c.userId)}>
                      <VisibilityIcon sx={{ fontSize: 20 }} />
                    </IconButton>
                    <Tooltip
                      title={
                        !isGranted(paths.customers.list, PERMISSION_TYPE_ENUM.Delete)
                          ? "Bạn không có quyền xoá"
                          : ""
                      }
                    >
                      <IconButton
                        size="small"
                        color="error"
                        sx={{ marginLeft: "8px" }}
                        disabled={!isGranted(paths.customers.list, PERMISSION_TYPE_ENUM.Delete)}
                        onClick={() => setConfirmDialog({ open: true, customer: c })}
                      >
                        <Delete sx={{ fontSize: 20 }} />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={9} align="center">
                  Không có khách hàng
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Box>
      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        rowsPerPageOptions={rowPerPageOptionsDefault}
        labelRowsPerPage="Số dòng mỗi trang" // Thay đổi văn bản ở phần này
      />

      <TitleDialog
        title={`Xóa ${confirmDialog?.customer?.fullname}`}
        open={confirmDialog.open}
        handleClose={handleCloseConfirmDialog}
        handleSubmit={handleConfirmDelete}
        submitBtnTitle="Xác nhận"
      >
        <Box>
          <Typography>Bạn có chắc muốn xóa khách "{confirmDialog?.customer?.fullname}"?</Typography>
        </Box>
      </TitleDialog>
    </>
  );
}
