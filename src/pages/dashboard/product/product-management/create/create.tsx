import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { Box, Grid, Button, CircularProgress, IconButton } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useFormik } from "formik";
import * as Yup from "yup";
import DashboardLayout from "@/src/layouts/dashboard";
import { tokens } from "@/src/locales/tokens";
import { useProduct } from "@/src/api/hooks/dashboard/product/use-product";
import { useService } from "@/src/api/hooks/dashboard/product/use-service";
import { useStoreId } from "@/src/hooks/use-store-id";
import { StorageService } from "nextjs-api-lib";
import {
  ProductFormValues,
  VariantType,
  initialProductValues,
} from "../../../../../types/product/form";
import { mockProductData } from "../../../../../_mock/mock-data";
import { FileType } from "@/src/constants/file-types";
import { useWarehouse } from "@/src/api/hooks/dashboard/product/use-warehouse";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import { isDevelopment } from "@/src/utils/env-helper";
import { useSnackbar } from "notistack";

// Components
import TypeSelection from "./components/type-selection";
import MediaUpload from "./components/media-upload";
import Price from "./components/price";
import PurchaseQuantity from "./components/purchase-quantity";
import VariantSettings from "./components/variant-settings";
import ShippingInformation from "./components/shipping-information";
import SeoSettings from "./components/seo-settings";
import { paths } from "@/src/paths";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { ProductItemOptionGroup } from "@/src/components/product/ProductItemOptionGroup";
import { useItemOption } from "@/src/api/hooks/item-option/use-item-option";
import BasicInformation from "./components/basic-information";
import DisplaySettings from "./components/display-settings";
import CategorySelection from "./components/category-selection";
import { Padding } from "@/src/styles/CommonStyle";
import ProductTaxRate from "./components/product-tax-rate";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { CommonMediaUpload } from "@/src/components/common-media-upload";
import { FILE_SIZE_2MB, MAX_FILE_IMAGE } from "@/src/constants/constant";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { ImageProcessor } from "@/src/utils/image-processor";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { GetProductCategoryRequest } from "@/src/api/types/product-category.types";
import { stripHtmlAndSpaces } from "@/src/components/react-quill-editor";
import DOMPurify from "dompurify";

const PRODUCT_TYPE_STORAGE_KEY = "productTypeTab";

type ItemType = "Product" | "Service";

export const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};
// Validation schema for the entire form
export const validationSchema = Yup.object().shape({
  // Basic Information
  itemsName: Yup.string()
    .required("Vui lòng nhập tên sản phẩm")
    .max(255, "Tên sản phẩm không được vượt quá 255 ký tự"),

  description: Yup.string().test(
    "max-length-without-html-and-spaces",
    "Mô tả không được vượt quá 4000 ký tự",
    function (value) {
      const textOnly = stripHtmlAndSpaces(value);
      return textOnly.length <= 4000;
    }
  ),
  // Category
  categoryIds: Yup.array()
    .of(Yup.string().required("Danh mục không hợp lệ")) // Mảng chứa chuỗi, mỗi chuỗi phải hợp lệ
    .min(1, "Vui lòng chọn ít nhất một danh mục") // Yêu cầu ít nhất 1 phần tử
    .required("Vui lòng chọn danh mục"), // Required cho cả mảng

  priceCapital: Yup.number()
    .required("Vui lòng nhập giá vốn")
    .min(0, "Giá vốn phải lớn hơn 0")
    .test("is-number", "Giá vốn phải là số", (value) => {
      return !isNaN(value);
    }),

  priceReal: Yup.number()
    .required("Vui lòng nhập giá niêm yết")
    .min(0, "Giá niêm yết phải lớn hơn 0")
    .test("is-number", "Giá niêm yết phải là số", (value) => {
      return !isNaN(value);
    })
    .test("greater-than-price", "Giá niêm yết phải lớn hơn hoặc bằng giá bán", function (value) {
      const { price } = this.parent;
      if (!value || !price) return true;
      return Number(value) >= Number(price);
    }),

  price: Yup.number()
    .required("Vui lòng nhập giá bán")
    .min(0, "Giá bán phải lớn hơn 0")
    .test("is-number", "Giá bán phải là số", (value) => {
      return !isNaN(value);
    })
    .test("price-validation", "Giá bán nhỏ hơn hoặc bằng giá niêm yết", function (value) {
      const { priceCapital, priceReal } = this.parent;

      if (!value || !priceCapital || !priceReal) return true;
      const priceNum = Number(value);
      const priceCapitalNum = Number(priceCapital);
      const priceRealNum = Number(priceReal);
      return priceNum <= priceCapitalNum || priceNum <= priceRealNum;
    }),

  // Media validation
  images: Yup.array()
    .min(0, "Vui lòng thêm ít nhất 1 ảnh")
    .max(20, "Tối đa 20 ảnh")
    .test("has-valid-files", "File không hợp lệ", (value) => {
      if (!value) return false;
      return value.every((file) => {
        // Check file type
        const isValidType = file.type === "IMAGE" || file.type === "VIDEO";
        // Add any additional validations here
        return isValidType;
      });
    }),

  // Shipping validation for Product type
  warehouseId: Yup.string().when("itemsType", {
    is: "Product",
    then: () => Yup.string().required("Vui lòng chọn kho hàng"),
  }),

  itemsWeight: Yup.number().when("itemsType", {
    is: "Product",
    then: () =>
      Yup.number()
        .typeError("Vui lòng nhập số")
        .min(0, "Khối lượng phải lớn hơn hoặc bằng 0")
        .required("Vui lòng nhập khối lượng sản phẩm"),
  }),

  itemsLength: Yup.number().when("itemsType", {
    is: "Product",
    then: () =>
      Yup.number()
        .typeError("Vui lòng nhập số")
        .min(0, "Chiều dài phải lớn hơn hoặc bằng 0")
        .required("Vui lòng nhập chiều dài sản phẩm"),
  }),

  quantityPurchase: Yup.number().when("itemsType", {
    is: "Product",
    then: () =>
      Yup.number()
        .typeError("Vui lòng nhập số")
        .min(0, "Số lượng mua tối đa phải lớn hơn hoặc bằng 0")
        .max(1000, "Số lượng mua tối đa không được vượt quá 1000"),
    otherwise: () => Yup.number().notRequired(),
  }),

  itemsWidth: Yup.number().when("itemsType", {
    is: "Product",
    then: () =>
      Yup.number()
        .typeError("Vui lòng nhập số")
        .min(0, "Chiều rộng phải lớn hơn hoặc bằng 0")
        .required("Vui lòng nhập chiều rộng sản phẩm"),
  }),

  itemsHeight: Yup.number().when("itemsType", {
    is: "Product",
    then: () =>
      Yup.number()
        .typeError("Vui lòng nhập số")
        .min(0, "Chiều cao phải lớn hơn hoặc bằng 0")
        .required("Vui lòng nhập chiều cao sản phẩm"),
  }),

  // Variant validation
  isVariant: Yup.boolean(),
  listVariant: Yup.array().when("isVariant", {
    is: true,
    then: (schema) =>
      schema.min(1, "Vui lòng thêm ít nhất một phiên bản").required("Vui lòng thêm phiên bản"),
    otherwise: (schema) => schema.notRequired(),
  }),
});

// Define common snackbar configuration
const SNACKBAR_CONFIG = {
  autoHideDuration: 3000,
  anchorOrigin: {
    vertical: "top" as const,
    horizontal: "right" as const,
  },
};

// Define validation error message function
const showValidationError = (message: string, enqueueSnackbar: any) => {
  enqueueSnackbar(message, {
    variant: "error",
    ...SNACKBAR_CONFIG,
  });
};

export interface ExistingMediaFile {
  link?: string;
  url?: string;
  type?: FileType;
  mediaFileId?: string;
  previewUrl?: string;
}

const CreateProduct = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { detailShop } = useShop();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [warehouses, setWarehouses] = useState<any[]>([]);
  const [extraItemOptionGroups, setExtraItemOptionGroups] = useState([]);
  const { getWarehouse } = useWarehouse();
  const { createProduct, getProductDetail, updateProduct } = useProduct();
  const { createService, getServiceDetail, updateService } = useService();
  const [shopTaxRate, setShopTaxRate] = useState<number>(0);
  const { itemOptionUser } = useItemOption();
  const storeId = useStoreId();
  const partnerId = StorageService.get("partnerId") || "";
  const mounted = useRef(false);
  const [categories, setCategories] = useState<any[]>([]);
  const { getProductCategoryTree } = useProductCategory();
  const [isLoading, setIsLoading] = useState(true);
  const [isEdit, setIsEdit] = useState(false);
  const { id, type } = router.query;
  const { enqueueSnackbar } = useSnackbar();
  const [existingFiles, setExistingFiles] = useState<ExistingMediaFile[]>([]);
  const [localFiles, setLocalFiles] = useState<File[]>([]);
  const { uploadFile } = useMedia();
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { getGroups } = useMedia();
  const [errorMsg, setErrorMsg] = useState<string>("");

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data: GetGroupFileRequest = {
          ShopId: storeId,
          Skip: 0,
          Limit: 10,
        };
        const response = await getGroups(data);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
  }, [storeId]);

  // Initialize itemsType
  const [itemsType, setItemsType] = useState<ItemType>(() => {
    const { type } = router.query;
    if (type === "Product" || type === "Service") {
      return type as ItemType;
    }
    const savedType = localStorage.getItem(PRODUCT_TYPE_STORAGE_KEY);
    if (savedType === "Product" || savedType === "Service") {
      return savedType as ItemType;
    }
    return "Product";
  });

  // Check if in edit mode
  useEffect(() => {
    setIsEdit(Boolean(id));
  }, [id]);

  useEffect(() => {
    const fetchDetailShop = async () => {
      const res = await detailShop(storeId);
      if (res && res?.status === 200) {
        setShopTaxRate(res?.data?.defaultTaxRate);
      }
    };
    fetchDetailShop();
  }, [storeId]);

  useEffect(() => {
    const fetchItemDetail = async () => {
      if (isEdit && id && storeId) {
        setIsLoading(true);
        try {
          const response = await (itemsType === "Product"
            ? getProductDetail(storeId, id as string)
            : getServiceDetail(storeId, id as string));

          const detail = response.data as any;

          // Update form values
          formik.setValues({
            // Basic info
            itemsId: detail.itemsId || "",
            itemsCode: detail.itemsCode || "",
            itemsType: detail.itemsType || "Product",
            itemsName: detail.itemsName || "",
            itemsInfo: detail.itemsInfo || "",
            description: detail.itemsInfo || "",

            // Categories
            categoryIds: detail.categoryIds || null,

            // Display settings
            isVisible: detail.isVisible ?? true,
            isTop: detail.isTop ?? false,
            typePublish: detail.typePublish || "Publish",
            status: detail.status || "Actived",
            itemsPosition: detail.itemsPosition || 0,

            // Media
            images: detail.images || [],

            // Variants
            isVariant: Boolean(detail.isVariant),
            listVariant: detail.listVariant || [],

            // Pricing
            priceCapital: detail.priceCapital || 0,
            price: detail.price || 0,
            priceReal: detail.priceReal || 0,

            // Quantity
            quantity: detail.quantity || 0,
            quantityPurchase: detail.quantityPurchase || 0,
            quantityPurchaseMin: detail.quantityPurchaseMin || 1,
            quantityPurchaseMax: detail.quantityPurchaseMax || 0,
            sold: 0, // Add missing required field

            // Shipping Information
            warehouseId: detail.warehouseId || "",
            itemsWeight: detail.itemsWeight || 0,
            itemsLength: detail.itemsLength || 0,
            itemsWidth: detail.itemsWidth || 0,
            itemsHeight: detail.itemsHeight || 0,

            // SEO
            seoTags: detail.seoTags || [
              {
                pageTitle: "",
                pageDesc: "",
                tags: "",
                url: "",
              },
            ],
            extraItemOptionGroups: detail.extraItemOptionGroups || [],
            transportType: detail.transportType || [],

            // System fields
            shopId: storeId,
            partnerId: partnerId as string,
            created: detail.created,
            updated: detail.updated,
          });
          if (detail.extraItemOptionGroups.length > 0) {
            const listItemOptionGroupId = detail.extraItemOptionGroups.map(
              (i) => i.itemOptionGroupId
            );
            const listItemOptionUserResponse = await itemOptionUser(listItemOptionGroupId);
            if (listItemOptionUserResponse?.data) {
              const listItemOptionUser = listItemOptionUserResponse.data?.data;
              setExtraItemOptionGroups(listItemOptionUser);
            }
          }

          // Update itemsType if different from current
          if (detail.itemsType !== itemsType) {
            setItemsType(detail.itemsType as ItemType);
          }
        } catch (error) {
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchItemDetail();
  }, [isEdit, id, storeId]);

  const handleSubmit = async (values: ProductFormValues) => {
    const errors = await formik.validateForm();
    if (Object.keys(errors).length > 0) {
      // Show first error message
      const firstError = Object.values(errors)[0];
      if (firstError) {
        showValidationError(String(firstError), enqueueSnackbar);
      }
      return;
    }

    if (!validateForm(values, enqueueSnackbar)) {
      return;
    }

    setIsSubmitting(true);
    try {
      const requestData = {
        ...values,
        partnerId,
        shopId: storeId,
        warehouseId: values.warehouseId || warehouses[0]?.warehouseId,
        itemsType,
        description: DOMPurify.sanitize(formik.values.description),
        priceCapital: values.isVariant == true ? 0 : values.priceCapital,
        price: values.isVariant == true ? 0 : values.price,
        priceReal: values.isVariant == true ? 0 : values.priceReal,
        quantity: values.isVariant == true ? 0 : values.quantity,
        quantityPurchase: values.isVariant == true ? 0 : values.quantityPurchase,
        listVariant: values.isVariant == false ? [] : values.listVariant,
        itemsInfo: values?.description,
        isTop: values.isTop,
        isShow: values.typePublish === "Publish" ? true : false,
        sold: values.sold,
        itemsPosition: Number(values.itemsPosition),
        customTaxRate: values.customTaxRate,
      };

      if (itemsType === "Product") {
        if (isEdit) {
          await updateProduct({ ...requestData, itemsId: id });
          enqueueSnackbar("Cập nhật sản phẩm thành công", { variant: "success" });
        } else {
          await createProduct(requestData);
          enqueueSnackbar("Tạo sản phẩm thành công", { variant: "success" });
        }
      } else {
        if (isEdit) {
          await updateService({ ...requestData });
          enqueueSnackbar("Cập nhật dịch vụ thành công", { variant: "success" });
        } else {
          await createService(requestData);
          enqueueSnackbar("Tạo dịch vụ thành công", { variant: "success" });
        }
      }
      if (isEdit) return;
      // Redirect after success
      router.push(paths.dashboard.product.productManagement);
    } catch (error) {
      showValidationError("Có lỗi xảy ra", enqueueSnackbar);
    } finally {
      setIsSubmitting(false);
    }
  };

  const MAX_TOTAL_IMAGES = 20;

  const countVariantImages = (variants: any[]) =>
    variants.filter((v) => v?.variantImage?.link).length;
  // Handle form submission
  const handleSubmitClick = async () => {
    try {
      const variantImageCount = countVariantImages(formik.values.listVariant || []);
      const totalImages = localFiles.length + existingFiles.length + variantImageCount;

      if (totalImages > MAX_TOTAL_IMAGES) {
        enqueueSnackbar(`Không thể chọn quá ${MAX_FILE_IMAGE} ảnh cho sản phẩm và các biến thể!`, {
          variant: "error",
        });
        return;
      }
      // Validate các trường khác trước
      const errors = await formik.validateForm();
      if (Object.keys(errors).length > 0) {
        const firstError = Object.values(errors)[0];
        if (firstError) {
          showValidationError(String(firstError), enqueueSnackbar);
          scrollToTop();
          return;
        }
        return;
      }
      Object.keys(formik.values).forEach((key) => {
        formik.setFieldTouched(key, true, false);
      });

      // Nếu chưa có ảnh nào
      // if (localFiles.length === 0 && (!formik.values.images || formik.values.images.length === 0)) {
      if (localFiles.length === 0 && existingFiles.length === 0) {
        showValidationError("Bạn cần chọn ít nhất 1 ảnh!", enqueueSnackbar);
        scrollToTop();
        return;
      }

      // Upload files nếu có
      let uploadedFiles = [];
      if (localFiles.length > 0) {
        for (const file of localFiles) {
          const processedFile = await ImageProcessor.processImage(file);
          const data: CreateFileGroupRequest = {
            FileUpload: processedFile,
            ShopId: storeId,
            GroupFileId: defaultGroupId,
            RefType: itemsType === "Product" ? RefType.Product : RefType.Service,
            RefId: "",
          };
          const response = await uploadFile(data);
          uploadedFiles.push(response.data);
        }
      }
      const newImages = uploadedFiles.map((file) => ({
        type: file.type === "IMAGE" ? FileType.IMAGE : FileType.VIDEO,
        link: file.url || file.link,
        mediaFileId: file.mediaFileId,
      }));

      const updatedImages = [...(existingFiles || []), ...newImages];

      // Cập nhật images cho Formik (KHÔNG await)
      if (updatedImages.length > 0) {
        formik.values.images = [...formik.values.images, ...updatedImages];
        formik.setFieldValue("images", updatedImages, true);

        // Validate field
        await formik.validateField("images");

        // Kiểm tra với updatedImages thay vì formik.values.images
        if (updatedImages.length === 0) {
          showValidationError("Bạn cần chọn ít nhất 1 ảnh!", enqueueSnackbar);
          scrollToTop();
          return;
        }

        setLocalFiles([]);

        // Gọi submit formik
        formik.handleSubmit();
      }
    } catch (error) {
      scrollToTop();
    }
  };

  // Initialize formik with validation schema
  const formik = useFormik<ProductFormValues>({
    initialValues: {
      ...initialProductValues,
      shopId: storeId,
      partnerId: partnerId as string,
      itemsType,
    },
    validationSchema,
    validateOnChange: true,
    onSubmit: handleSubmit,
  });

  // Single useEffect for initial data fetch
  useEffect(() => {
    const fetchInitialData = async () => {
      if (!storeId) return;

      setIsLoading(true);
      try {
        const params: GetProductCategoryRequest = {
          categoryType: itemsType,
          shopId: storeId,
          partnerId: StorageService.get("partnerId") as string | null,
          paging: {
            name: "Created",
            nameType: "Created",
            sortType: "desc",
            sort: "desc",
            pageIndex: 0,
            pageSize: 100,
          },
        };
        const [categoryResponse, warehouseResponse] = await Promise.all([
          getProductCategoryTree(params),
          getWarehouse(0, 100),
        ]);

        const categoryList = categoryResponse.data.data.result || [];
        const warehouseList = warehouseResponse.data.data || [];

        setCategories(categoryList);
        setWarehouses(warehouseList);

        // Chỉ set default values khi có data và chưa có giá trị
        if (categoryList.length > 0 && !formik.values.categoryIds) {
          // Đảm bảo categoryId tồn tại trước khi set
          const defaultCategory = categoryList[0];
          if (defaultCategory?.categoryId) {
            formik.setFieldValue("categoryId", defaultCategory.categoryId, false);
          }
        }

        if (warehouseList.length > 0 && !formik.values.warehouseId) {
          const defaultWarehouse = warehouseList[0];
          if (defaultWarehouse?.warehouseId) {
            formik.setFieldValue("warehouseId", defaultWarehouse.warehouseId, false);
          }
        }
      } catch (error) {
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialData();
  }, [storeId, itemsType, formik.setFieldValue]);

  // Mock data handler
  const handleMockData = () => {
    const defaultWarehouseId = warehouses.length > 0 ? warehouses[0].warehouseId : "";

    // Get current form values that we want to preserve
    const currentImages = formik.values.images;
    const currentVariants = formik.values.listVariant;
    const isVariant = formik.values.isVariant;
    const currentItemsType = formik.values.itemsType;

    // Get mock data but pass existing images
    const mockData = mockProductData(
      defaultWarehouseId,
      storeId,
      partnerId as string,
      formik.values.categoryIds,
      isVariant,
      currentItemsType
    );

    formik.setValues({
      ...mockData,
      images: currentImages, // Ensure we keep the current images
      isVariant,
      listVariant: currentVariants,
      itemsType: currentItemsType,
    });
  };

  // Update URL and localStorage when type changes
  useEffect(() => {
    localStorage.setItem(PRODUCT_TYPE_STORAGE_KEY, itemsType);
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, type: itemsType },
      },
      undefined,
      { shallow: true }
    );

    // Update form values when type changes
    formik.setFieldValue("itemsType", itemsType);
  }, [itemsType]);

  // Validate form fields
  const validateForm = (values: ProductFormValues, enqueueSnackbar: any) => {
    // Kiểm tra variant
    if (values.isVariant && (!values.listVariant || values.listVariant.length === 0)) {
      showValidationError("Vui lòng thêm ít nhất một phiên bản", enqueueSnackbar);
      return false;
    }

    // Reset variant nếu không có dữ liệu
    if (values.isVariant && values.listVariant?.length === 0) {
      formik.setFieldValue("isVariant", false);
    }

    return true;
  };

  // Loading spinner centered
  if (isLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
          position: "relative",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // get Title by tab itemsType
  const getTitleByTab = (itemsType: String) => {
    return itemsType === "Product"
      ? isEdit
        ? "Hàng hóa/Sản phẩm/Cập nhật sản phẩm"
        : "Hàng hóa/Sản phẩm/Thêm sản phẩm mới"
      : isEdit
      ? "Hàng hóa/Dịch vụ/Cập nhật dịch vụ"
      : "Hàng hóa/Dịch vụ/Thêm dịch vụ mới";
  };
  const handleCancel = () => {
    router.back();
  };

  const handleFilesChange = (files: File[]) => {
    setLocalFiles(files);
  };

  // Callback khi xóa file preview (có thể dùng để xóa file existing nếu cần)
  const handleRemove = (index: number, isExisting: boolean) => {
    if (isExisting) {
      const updated = [...existingFiles];
      updated.splice(index, 1);
      setExistingFiles(updated);
    } else {
      const localIdx = index - existingFiles.length;
      setLocalFiles((prev) => prev.filter((_, i) => i !== localIdx));
      // Không cần setLocalPreviews ở đây, useEffect sẽ tự cập nhật
    }
    // Local file sẽ tự động cập nhật qua handleFilesChange
  };

  return (
    <DashboardLayout>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
          position: "relative",
          padding: Padding,
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            mb: 3,
            paddingBottom: "20px",
            marginBottom: "20px",
            borderBottom: "1px solid #bdbdbd",
          }}
        >
          <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <TitleTypography sx={{ fontSize: "20px !important", lineHeight: "1" }}>
            {getTitleByTab(itemsType)}
          </TitleTypography>
        </Box>

        <Box>
          {/* Type Selection */}
          {/* <Box sx={{ mb: 3 }}>
            <TypeSelection itemsType={itemsType} isEdit={isEdit} setItemsType={setItemsType} />
          </Box> */}

          {/* Main Content */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 3,
                }}
              >
                <BasicInformation formik={formik} itemsType={itemsType} />
                {/* <MediaUpload formik={formik} /> */}
                <CommonMediaUpload
                  caption="Thêm ảnh"
                  maxFiles={20}
                  accept={{
                    "image/*": [".png", ".jpg", ".jpeg"],
                  }}
                  maxSize={FILE_SIZE_2MB} // 2MB
                  existingFiles={existingFiles}
                  localFiles={localFiles}
                  setLocalFiles={setLocalFiles}
                  onFilesChange={handleFilesChange}
                  onRemove={handleRemove}
                  defaultGroupId={defaultGroupId}
                  setExistingFiles={setExistingFiles}
                  errorMsg={errorMsg}
                  setErrorMsg={setErrorMsg}
                />
                {itemsType === "Product" && <VariantSettings formik={formik} />}
                {itemsType === "Product" && (
                  <ProductItemOptionGroup
                    formik={formik}
                    listDefaultItemOptionGroups={extraItemOptionGroups}
                  />
                )}

                {/* Chỉ hiển thị khi không phải là variant */}
                {!formik.values.isVariant && (
                  <>
                    <Price formik={formik} />
                    <PurchaseQuantity formik={formik} />
                  </>
                )}
                {itemsType === "Product" && (
                  <ShippingInformation formik={formik} warehouses={warehouses} />
                )}

                <SeoSettings formik={formik} />
              </Box>
            </Grid>

            {/* Right Sidebar */}
            <Grid item xs={12} md={4}>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
                <DisplaySettings formik={formik} />
                <CategorySelection formik={formik} categories={categories} itemsType={itemsType} />
                <ProductTaxRate formik={formik} shopTaxRate={shopTaxRate} />
              </Box>
            </Grid>
          </Grid>

          {/* Footer Actions */}
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              position: "relative",
              padding: Padding,
              paddingBottom: "80px",
            }}
          >
            <Box
              sx={{
                position: "fixed",
                bottom: 0,
                left: 0,
                right: 0,
                padding: "10px 32px",
                borderTop: 1,
                borderColor: "#e5e6e7",
                display: "flex",
                justifyContent: "flex-end",
                gap: 2,
                backgroundColor: "background.paper",
                zIndex: 1000,
              }}
            >
              <Button
                sx={{ color: "#2654FE", textTransform: "none" }}
                variant="outlined"
                onClick={() => router.back()}
              >
                {t(tokens.common.cancel)}
              </Button>

              {isDevelopment && (
                <Button
                  variant="outlined"
                  sx={{ color: "#2654FE", textTransform: "none" }}
                  onClick={handleMockData}
                >
                  Điền dữ liệu mẫu
                </Button>
              )}
              <Button
                sx={{
                  background: "#2654FE",
                  textTransform: "none",
                }}
                variant="contained"
                onClick={handleSubmitClick}
                disabled={isSubmitting}
              >
                {isSubmitting ? <CircularProgress size={24} /> : t(tokens.common.save)}
              </Button>
            </Box>
          </Box>
        </Box>
      </Box>
    </DashboardLayout>
  );
};

export default CreateProduct;
