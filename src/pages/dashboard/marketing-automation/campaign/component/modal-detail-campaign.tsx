import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  TextField,
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Select,
  MenuItem,
  Paper,
  Grid,
  IconButton,
  Divider,
  Autocomplete,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TablePagination,
  CircularProgress,
  Stack,
  InputAdornment,
  Tooltip,
  Skeleton,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { Upload, CalendarToday, UploadFile, Search } from "@mui/icons-material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import ModalImportCustomer from "./modal-import-customer";
import { useZaloAutomation } from "@/src/api/hooks/zalo-automation/zalo-automation";
import { useStoreId } from "@/src/hooks/use-store-id";
import { ZnsTemplateDto } from "@/src/api/services/zalo-automation/zalo-automation.service";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useCampaign } from "@/src/api/hooks/campaign/campaign";
import {
  CampaignDto,
  CampaignUserQueryParams,
  IBodyCreateCampaign,
  IBodyImportExcelListUserCampaign,
} from "@/src/api/services/campaign/campaign.service";
import { ClearIcon, DateTimePicker } from "@mui/x-date-pickers";
import useSnackbar from "@/src/hooks/use-snackbar";
import _ from "lodash";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { formatPrice } from "@/src/api/types/membership.types";
import { CURRENCY_UNIT } from "@/src/constants/constant";

export interface IParamsZns {
  name: string;
  type?: string;
  require: boolean;
  maxLength: number;
  minLength: number;
  acceptNull: boolean;
}

export const campaignSchema = yup.object().shape({
  campaignName: yup
    .string()
    .required("Vui lòng nhập tên chiến dịch")
    .max(100, "Tên chiến dịch không được vượt quá 100 ký tự"),
  messageTemplate: yup.object().nullable().required("Vui lòng chọn mẫu tin nhắn"),
  campaignDesc: yup.string().max(500, "Mô tả không được vượt quá 500 ký tự"),
  runTime: yup.string().required("Vui lòng chọn thời gian chạy"),
  scheduledDate: yup
    .date()
    .nullable()
    .when("runTime", {
      is: "SpecificDateTime",
      then: (schema) => schema.required("Vui lòng chọn ngày giờ chạy"),
    }),
});
interface CampaignFormValues {
  campaignName: string;
  messageTemplate: any; // Update with the proper type
  campaignDesc: string;
  runTime: string;
  scheduledDate: Dayjs | null;
}

export interface CampaignUserDto {
  campaignId: string;
  campaignUserId: string;
  phone: string;
  status: string;
  templateData: string;
  description: string | null;
  price: number;
  messageId: string | null;
  sentTime: string;
  deliveryTime: string;
  sendingMode: string | null;
  createdDate: string;
  modifiedDate: string | null;
  createdBy: string;
  modifiedBy: string | null;
  isDeleted: boolean;
  deletedAt: string | null;
  id: string;
}
const getStatusConfig = (status: string) => {
  switch (status) {
    case "NotSent":
      return {
        label: "Chưa gửi lên Zalo",
        color: "rgb(99, 115, 129)",
        backgroundColor: "rgba(145, 158, 171, 0.16)",
      };
    case "Processing":
      return {
        label: "Đang xử lý trên Zalo",
        color: "rgb(255, 171, 0)",
        backgroundColor: "rgba(255, 171, 0, 0.16)",
      };
    case "Success":
      return {
        label: "Gửi thành công",
        color: "rgb(34, 154, 22)",
        backgroundColor: "rgba(84, 214, 44, 0.16)",
      };
    case "Failed":
      return {
        label: "Gửi thất bại",
        color: "rgb(183, 33, 54)",
        backgroundColor: "rgba(255, 72, 66, 0.16)",
      };
    case "Cancelled":
      return {
        label: "Đã hủy gửi",
        color: "rgb(99, 115, 129)",
        backgroundColor: "rgba(145, 158, 171, 0.16)",
      };
    case "QuotaExceeded":
      return {
        label: "Vượt quá quota Zalo cho phép",
        color: "rgb(183, 33, 54)",
        backgroundColor: "rgba(255, 72, 66, 0.16)",
      };
    case "InsufficientFunds":
      return {
        label: "Tài khoản hết tiền",
        color: "rgb(183, 33, 54)",
        backgroundColor: "rgba(255, 72, 66, 0.16)",
      };
    default:
      return {
        label: "Không xác định",
        color: "rgb(99, 115, 129)",
        backgroundColor: "rgba(145, 158, 171, 0.16)",
      };
  }
};

export default function ModalDetailCampaignV2({
  // campaignType,
  // setCampaignType,
  campaignEdit,
  open,
  setOpen,
  fetchListCampaign,
}) {
  const { getListTemplateZns, getDetailTemplateZns, exportFileTemplateExcelZns } =
    useZaloAutomation();
  const {
    importFileExcelListUserCampaign,
    updateCampaign,
    getListUserOfCampaign,
    changeStatusOfCampaign,
    rerunCampaign,
  } = useCampaign();
  const [isOpenModalUploadFile, setIsOpenModalUploadFile] = useState<boolean>(false);
  const storeId = useStoreId();
  const [listTemplateMessage, setListTemplateMessage] = useState<ZnsTemplateDto[]>([]);
  const [selectedMessageTemplate, setSelectedMessageTemplate] = useState<ZnsTemplateDto | null>(
    null
  );
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [listParam, setListParam] = useState<IParamsZns[]>([]);
  const [file, setFile] = useState<File>(null);
  const [campaignCreated, setCampaignCreated] = useState<CampaignDto>();
  const snackbar = useSnackbar();
  const [listUserCampaign, setListUserCampaign] = useState<CampaignUserDto[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);

  const [searchPhone, setSearchPhone] = useState("");
  const [status, setStatus] = useState("all");
  const [dateRange, setDateRange] = useState({
    startDate: null,
    endDate: null,
  });

  const fetchListUserOfCampaign = useCallback(async () => {
    if (!campaignEdit?.campaignId || !open) return;

    setIsLoading(true);
    try {
      const param: CampaignUserQueryParams = {
        ShopId: storeId,
        CampaignId: campaignEdit.campaignId,
        FromDate: dateRange?.startDate
          ? dayjs(dateRange?.startDate).format("YYYY-MM-DD")
          : undefined,
        ToDate: dateRange?.endDate ? dayjs(dateRange?.endDate).format("YYYY-MM-DD") : undefined,
        Status: status === "all" ? undefined : status,
        Paging: {
          PageSize: rowsPerPage,
          PageIndex: page,
          Search: searchPhone.trim() || undefined,
        },
      };

      const response = await getListUserOfCampaign(param);

      if (response?.data?.data) {
        setListUserCampaign(response.data.data.result || []);
        setTotalCount(response.data.data.total || 0);
      }
    } catch (error) {
      console.error("Error fetching campaign users:", error);
      snackbar.error("Không thể tải danh sách người dùng chiến dịch");
    } finally {
      setIsLoading(false);
    }
  }, [
    campaignEdit?.campaignId,
    storeId,
    page,
    rowsPerPage,
    searchPhone,
    status,
    dateRange.startDate,
    dateRange.endDate,
    open,
  ]);

  useEffect(() => {
    if (open) {
      fetchListUserOfCampaign();
    }
  }, [
    fetchListUserOfCampaign,
    open,
    page,
    rowsPerPage,
    searchPhone,
    status,
    dateRange.startDate,
    dateRange.endDate,
  ]);

  const handlePageChange = (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const debouncedSearch = useCallback(
    _.debounce((value: string) => {
      setSearchPhone(value);
      setPage(0);
    }, 500),
    []
  );

  const handleChangeStatusOfCampaign = async () => {
    try {
      const response = await changeStatusOfCampaign(campaignEdit.shopId, campaignEdit.campaignId);
      if (response?.status === 200) {
        snackbar.success("Cập nhật trạng thái chiến dịch thành công");
        setOpen(false);
      }
    } catch (error) {
      snackbar.error("Không thể cập nhật trạng thái chiến dịch");
    }
  };

  const handleRerunCampaign = async () => {
    try {
      const response = await rerunCampaign(campaignEdit.shopId, campaignEdit.campaignId);
      if (response?.status === 200) {
        snackbar.success("Gửi lại chiến dịch thành công");
        setOpen(false);
      }
    } catch (error) {
      snackbar.error("Không thể gửi lại chiến dịch");
    }
  };

  return (
    <Box>
      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            p: 1,
            maxHeight: "90vh",
          },
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            px: 2,
            py: 1,
          }}
        >
          <Typography variant="h6" fontWeight="bold">
            Chi tiết chiến dịch
          </Typography>
        </Box>

        <Box sx={{ p: 2, paddingBottom: 0.5 }}>
          <Stack
            direction={{ xs: "column", sm: "row" }}
            // spacing={2}
            sx={{ mb: 3, display: "flex", flexWrap: "wrap", gap: "8px" }}
          >
            {/* <Box> */}
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DatePicker
                slotProps={{
                  textField: { size: "small", placeholder: "Từ ngày" },
                }}
                value={dateRange.startDate}
                onChange={(newValue) => {
                  console.log(newValue.toString());
                  if (newValue.toString() !== "Invalid Date") {
                    setDateRange((prev) => ({ ...prev, startDate: newValue }));
                    setPage(0);
                  }
                }}
                format="DD/MM/YYYY"
                sx={{ width: "220px" }}
              />
              <DatePicker
                slotProps={{ textField: { size: "small", placeholder: "Đến ngày" } }}
                value={dateRange.endDate}
                onChange={(newValue) => {
                  console.log({ newValue });

                  if (newValue.toString() !== "Invalid Date") {
                    setDateRange((prev) => ({ ...prev, endDate: newValue }));
                    setPage(0);
                  }
                }}
                format="DD/MM/YYYY"
                sx={{ width: "220px" }}
              />
            </LocalizationProvider>
            <FormControl size="small" sx={{ width: "220px" }}>
              <Select
                value={status}
                onChange={(e) => {
                  setStatus(e.target.value);
                  setPage(0);
                }}
                displayEmpty
              >
                <MenuItem value="all">Tất cả trạng thái</MenuItem>
                <MenuItem value="NotSent">Chưa gửi lên Zalo</MenuItem>
                <MenuItem value="Processing">Đang xử lý trên Zalo</MenuItem>
                <MenuItem value="Success">Gửi thành công</MenuItem>
                <MenuItem value="Failed">Gửi thất bại</MenuItem>
                <MenuItem value="Cancelled">Đã hủy gửi</MenuItem>
                <MenuItem value="QuotaExceeded">Vượt quá quota Zalo</MenuItem>
                <MenuItem value="InsufficientFunds">Tài khoản hết tiền</MenuItem>
              </Select>
            </FormControl>
            <TextField
              size="small"
              placeholder="Tìm kiếm số điện thoại..."
              onChange={(e) => debouncedSearch(e.target.value)}
              sx={{ width: "220px" }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
            <Tooltip title="Cập nhật trạng thái ZNS thủ công từ Zalo" placement="top">
              <Button
                variant="contained"
                size="small"
                color="primary"
                onClick={handleChangeStatusOfCampaign}
                disabled={!listUserCampaign?.length}
              >
                Cập nhật trạng thái
              </Button>
            </Tooltip>
            <Tooltip title="Gửi lại các trường hợp Chưa gửi lên Zalo" placement="top">
              <Button
                variant="contained"
                size="small"
                color="primary"
                onClick={handleRerunCampaign}
                disabled={!listUserCampaign?.length}
              >
                Gửi lại
              </Button>
            </Tooltip>
            {/* </Box> */}
          </Stack>
        </Box>

        <DialogContent sx={{ p: 2 }}>
          <TableContainer component={Paper}>
            <Table sx={{ minWidth: 650 }} size="small">
              <TableHead>
                <TableRow>
                  <TableCell>STT</TableCell>
                  <TableCell>Số điện thoại</TableCell>
                  <TableCell>Trạng thái</TableCell>
                  <TableCell>Ngày tạo</TableCell>
                  <TableCell>Giá ({CURRENCY_UNIT})</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      {Array.from({ length: rowsPerPage }).map((_, index) => (
                        <Skeleton key={index} variant="text" width="100%" height={40} />
                      ))}
                    </TableCell>
                  </TableRow>
                ) : Array.isArray(listUserCampaign) && listUserCampaign.length > 0 ? (
                  listUserCampaign.map((user, index) => (
                    <TableRow
                      key={user.campaignUserId}
                      sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                    >
                      <TableCell>{index + 1}</TableCell>

                      <TableCell>{user.phone}</TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            display: "inline-block",
                            px: 1.5,
                            py: 0.5,
                            borderRadius: 1,
                            color: getStatusConfig(user.status).color,
                            backgroundColor: getStatusConfig(user.status).backgroundColor,
                            fontWeight: 500,
                            fontSize: 13,
                          }}
                        >
                          {getStatusConfig(user.status).label}
                        </Box>
                      </TableCell>
                      <TableCell>
                        {user.createdDate
                          ? dayjs(user.createdDate).format("DD/MM/YYYY HH:mm:ss")
                          : ""}
                      </TableCell>
                      <TableCell
                        sx={
                          {
                            // fontWeight: 500,
                            // color: "success.main",
                          }
                        }
                      >
                        {formatPrice(user.price)}{" "}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                      <Typography variant="body2" color="text.secondary">
                        Không có dữ liệu
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
            <TablePagination
              component="div"
              count={totalCount}
              page={page}
              onPageChange={handlePageChange}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={handleRowsPerPageChange}
              labelRowsPerPage="Số hàng mỗi trang:"
              rowsPerPageOptions={rowPerPageOptionsDefault}
              labelDisplayedRows={({ from, to, count }) => `${from}-${to} của ${count}`}
            />
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
}
