import {
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Container,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  CircularProgress,
  Alert,
  Tooltip,
} from "@mui/material";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import React, { useEffect, useState } from "react";
import { useFunction } from "@/src/api/hooks/function/use-function";
import { formatPrice } from "@/src/api/types/membership.types";
import useSnackbar from "@/src/hooks/use-snackbar";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import { useAppDispatch, useAppSelector } from "@/src/redux/hooks";
import { getProfile } from "@/src/redux/slices/profileSlice";
import { CURRENCY_UNIT } from "@/src/constants/constant";

interface PackageDto {
  id: string;
  code: string;
  name: string;
  description: string[];
  detail: string[];
  price: number;
  durationDays: number;
  isActive: boolean;
}

const getTitleButtonPackage = (isActive: boolean, price: number, isDowngrade: boolean) => {
  if (isActive) return "Đang sử dụng";
  if (isDowngrade) return "Mua gói ngay";
  if (price > 0) return "Nâng cấp ngay";
  if (price === 0) return "Dùng thử";
  return "Mua gói ngay";
};

const renderFeatures = (functions: string[], descriptions: string[], packageIndex: number = 0) => {
  const packageStyles = [
    { bg: "#E8F0FE", icon: "#4285F4" },
    { bg: "#F3E5F5", icon: "#9C27B0" },
    { bg: "#FFF3E0", icon: "#FF9800" },
  ];

  const style = packageStyles[packageIndex % packageStyles.length];

  return (
    <>
      <Box
        sx={{
          backgroundColor: style.bg,
          borderRadius: 2,
          p: 2,
          mb: 2,
        }}
      >
        {Array.isArray(descriptions) &&
          descriptions.length > 0 &&
          descriptions.map((feature, index) => (
            <ListItem
              key={index}
              disableGutters
              sx={{
                display: "flex",
                alignItems: "center",
                py: 0.5,
              }}
            >
              <ListItemIcon sx={{ minWidth: "auto", marginRight: 1 }}>
                <CheckCircleOutlineIcon sx={{ color: style.icon, fontSize: 25 }} />
              </ListItemIcon>
              <ListItemText
                primary={feature}
                primaryTypographyProps={{ color: "#000", fontSize: "15px", fontWeight: 500 }}
              />
            </ListItem>
          ))}
      </Box>

      <Divider sx={{ my: 2, borderColor: "black" }} />

      {Array.isArray(functions) &&
        functions.length > 0 &&
        functions.map((func, index) => (
          <React.Fragment key={index}>
            <ListItem disableGutters sx={{ display: "flex", alignItems: "center", padding: 0.5 }}>
              <ListItemIcon sx={{ minWidth: "auto", marginRight: 1 }}>
                <CheckCircleOutlineIcon sx={{ color: "black", fontSize: 25 }} />
              </ListItemIcon>
              <ListItemText
                primary={func}
                primaryTypographyProps={{ color: "#000", fontSize: "15px", fontWeight: 500 }}
              />
            </ListItem>
          </React.Fragment>
        ))}
    </>
  );
};
const getDurationUnit = (durationDays: number): string => {
  if (durationDays <= 30) {
    return `${durationDays} ngày`;
  } else if (durationDays < 365) {
    const months = Math.round(durationDays / 30);
    return `tháng`;
  } else {
    const years = Math.round(durationDays / 365);
    return `năm`;
  }
};

const renderCard = (
  tier: PackageDto,
  handleOpenDialog: (plan: PackageDto) => void,
  index: number,
  getTitleButton: string,
  isDowngrade: boolean
) => {
  const packageStyles = [
    { bg: "#E8F0FE", icon: "#4285F4" },
    { bg: "#F3E5F5", icon: "#9C27B0" },
    { bg: "#FFF3E0", icon: "#FF9800" },
  ];

  const style = packageStyles[index % packageStyles.length];
  return (
    <Card
      key={tier.name}
      sx={{
        marginTop: index !== 1 ? "30px" : 0,
        marginBottom: index !== 1 ? "-30px" : 0,
        display: "flex",
        flexDirection: "column",
        borderRadius: 2,
        boxShadow: "0 4px 4px 0 #00000040",
        // opacity: tier.isActive ? 0.6 : 1, // Dim the active package
        "&:hover": {
          boxShadow: "0 0.5rem 1.875rem rgba(0,0,0,0.12)",
          transform: tier.isActive || isDowngrade ? "none" : "translateY(-0.5rem)", // Disable hover transform for active package
          transition: "all 0.3s ease-in-out",
        },
      }}
    >
      <CardContent sx={{ flexGrow: 1, p: 2, textAlign: "center" }}>
        <Typography gutterBottom sx={{ fontSize: 26, fontWeight: 600, color: style.icon }}>
          {tier.name}
        </Typography>
        <Typography
          sx={{
            mb: 2,
            display: "flex",
            justifyContent: "center",
            alignItems: "baseline",
            fontSize: "28px",
            fontWeight: 600,
          }}
        >
          {formatPrice(tier.price)}
          <Typography sx={{ ml: 0, color: "#000", fontWeight: "600", fontSize: "28px" }}>
            đ/ {getDurationUnit(tier.durationDays)}
          </Typography>
        </Typography>
        <Tooltip title={isDowngrade ? "Không thể hạ cấp gói dịch vụ" : ""}>
          <span>
            <Button
              sx={{
                mb: 2,
                width: "100%",
                backgroundColor: style.icon,
                color: "white",
                fontSize: "20px",
                textTransform: "none",
                padding: "10px",
                borderRadius: "20px",
                "&:hover": {
                  backgroundColor: style.icon,
                },
                "&.Mui-disabled": {
                  backgroundColor: style.icon,
                  color: "white",
                  opacity: 0.6,
                },
              }}
              onClick={() => handleOpenDialog(tier)}
              disabled={tier.isActive || isDowngrade}
            >
              {getTitleButton}
            </Button>
          </span>
        </Tooltip>
        <List disablePadding>{renderFeatures(tier.detail, tier.description, index)}</List>
      </CardContent>
    </Card>
  );
};

const PackageManager: React.FC = () => {
  const dispatch = useAppDispatch();
  const { profile } = useAppSelector((state) => state.profile);
  const { getAvailablePackages, purchasePackage, upgradePackage } = useFunction();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<PackageDto | null>(null);
  const [packages, setPackages] = useState<PackageDto[]>([]);
  const [activePackage, setActivePackage] = useState<PackageDto | null>(null);
  const [isLoadingPackages, setIsLoadingPackages] = useState(true);
  const [isLoadingDialog, setIsLoadingDialog] = useState(false);
  const [isActionSuccess, setIsActionSuccess] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<"wallet" | "transfer">("wallet");
  const snackbar = useSnackbar();
  const router = useRouter();

  const isUpgradeMode = !!activePackage;

  const fetchAvailablePackages = async () => {
    try {
      setIsLoadingPackages(true);
      const res = await getAvailablePackages();
      const allPackages = res.data.data;
      const activePkg = allPackages.find((pkg: PackageDto) => pkg.isActive) || null;
      setActivePackage(activePkg);
      // Show all packages, including active one
      setPackages(allPackages);
    } catch (error) {
      snackbar.error("Không thể tải danh sách gói dịch vụ");
    } finally {
      setIsLoadingPackages(false);
    }
  };

  useEffect(() => {
    fetchAvailablePackages();
  }, []);

  const handleOpenDialog = (plan: PackageDto) => {
    if (plan.isActive) return;

    // Prevent downgrading - don't open dialog for packages with lower price than active
    if (activePackage && plan.price < activePackage.price) {
      return;
    }

    setSelectedPlan(plan);
    setOpenDialog(true);
    setIsActionSuccess(false);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedPlan(null);
    setIsLoadingDialog(false);
    setIsActionSuccess(false);
  };

  const handleActionPackage = async () => {
    if (!selectedPlan) return;

    setIsLoadingDialog(true);
    const body = {
      packageId: selectedPlan.id,
      paymentMethod: paymentMethod === "wallet" ? "Wallet" : "Transfer",
      invoiceNumber: isUpgradeMode ? `INV-${Date.now()}` : undefined,
    };

    const adjustedPrice = selectedPlan.price;

    if (
      paymentMethod === "wallet" &&
      Number(profile?.balance || 0) !== null &&
      Number(profile?.balance || 0) < adjustedPrice
    ) {
      snackbar.error("Số dư ví không đủ để thực hiện giao dịch");
      setIsLoadingDialog(false);
      return;
    }

    try {
      const res = isUpgradeMode ? await upgradePackage(body) : await purchasePackage(body);
      if (res && res.status === 200) {
        fetchAvailablePackages();
        dispatch(getProfile());
        setIsActionSuccess(true);
      }
    } catch (error) {
      snackbar.error("Giao dịch thất bại, vui lòng thử lại");
    } finally {
      setIsLoadingDialog(false);
    }
  };

  const handlePaymentMethodChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPaymentMethod(event.target.value as "wallet" | "transfer");
  };

  return (
    <Container sx={{ paddingBlock: 2 }}>
      {!isLoadingPackages && (
        <Box
          sx={{
            textAlign: "center",
            mb: 1.5,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box>
            <Typography
              component="h1"
              sx={{ mb: 1, fontSize: "26px", fontWeight: "700", textAlign: "left" }}
            >
              {isUpgradeMode ? "Nâng cấp gói dịch vụ" : "Gói dịch vụ"}
            </Typography>
            <Typography sx={{ fontSize: "16px", color: "text.secondary", textAlign: "left" }}>
              {isUpgradeMode
                ? "Chọn gói dịch vụ mới để nâng cấp"
                : "Chọn gói dịch vụ phù hợp với bạn"}
            </Typography>
          </Box>

          {activePackage && (
            <Button
              variant="contained"
              onClick={() => router.push(paths.settings.service)}
              sx={{
                borderRadius: "50px",
                textTransform: "none",
                fontSize: "0.95rem",
                px: 3,
                py: 1,
                backgroundColor: "#6366F1",
                "&:hover": {
                  backgroundColor: "#5254cc",
                },
              }}
            >
              Quản lý gói dịch vụ
            </Button>
          )}
        </Box>
      )}
      {isLoadingPackages ? (
        <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
          <CircularProgress />
        </Box>
      ) : packages.length === 0 ? (
        <Alert severity="info">
          {isUpgradeMode
            ? "Không có gói dịch vụ nào để nâng cấp."
            : "Không có gói dịch vụ nào khả dụng."}
        </Alert>
      ) : (
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: {
              xs: "1fr",
              md: "repeat(auto-fit, minmax(300px, 360px))",
            },
            gap: "1rem",
            justifyContent: "center",
            mx: "auto",
          }}
        >
          {packages.map((tier: PackageDto, index: number) => {
            // Check if this package is a downgrade from current package
            const isDowngrade = activePackage ? tier.price < activePackage.price : false;
            return renderCard(
              tier,
              handleOpenDialog,
              index,
              getTitleButtonPackage(tier.isActive, tier.price, isDowngrade),
              isDowngrade
            );
          })}
        </Box>
      )}
      {selectedPlan && (
        <Dialog
          open={openDialog}
          onClose={(event, reason) => {
            if (reason === "backdropClick" || reason === "escapeKeyDown") {
              handleCloseDialog();
            }
          }}
          PaperProps={{
            sx: {
              borderRadius: "24px",
              p: 3,
              maxWidth: "400px",
              width: "100%",
              boxShadow: "0px 8px 24px rgba(0, 0, 0, 0.15)",
            },
          }}
        >
          <DialogTitle
            sx={{
              fontWeight: "700",
              fontSize: "1.25rem",
              p: 0,
              pb: 1,
              textAlign: "center",
            }}
          >
            {isActionSuccess
              ? isUpgradeMode
                ? "Nâng cấp thành công"
                : "Thanh toán thành công"
              : isUpgradeMode
              ? "Thanh toán nâng cấp"
              : "Thanh toán"}
          </DialogTitle>
          <DialogContent sx={{ p: 0, mt: 2 }}>
            {isLoadingDialog ? (
              <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
                <CircularProgress />
              </Box>
            ) : isActionSuccess ? (
              <Box sx={{ textAlign: "center", mb: 3 }}>
                <CheckCircleOutlineIcon sx={{ color: "green", fontSize: 48, mb: 2 }} />
                <Typography sx={{ fontWeight: "600", fontSize: "1.1rem", mb: 1 }}>
                  {isUpgradeMode
                    ? `Nâng cấp gói ${selectedPlan.name} thành công!`
                    : `Mua gói ${selectedPlan.name} thành công!`}
                </Typography>
                <Typography sx={{ fontSize: "0.9rem", color: "text.secondary" }}>
                  Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi.
                </Typography>
              </Box>
            ) : (
              <>
                <Box sx={{ textAlign: "center", mb: 3 }}>
                  <Typography sx={{ fontWeight: "600", fontSize: "1.1rem" }}>
                    Gói: {selectedPlan.name}
                  </Typography>
                  <Typography sx={{ fontWeight: "600", fontSize: "1.2rem", color: "#6366F1" }}>
                    {isUpgradeMode ? "Giá nâng cấp" : "Giá"}: {formatPrice(selectedPlan.price)}{" "}
                    đ/năm
                  </Typography>
                </Box>
                <FormControl component="fieldset" sx={{ width: "100%", mb: 2 }}>
                  <Typography sx={{ fontWeight: "500", mb: 1 }}>Phương thức thanh toán:</Typography>
                  <RadioGroup
                    value={paymentMethod}
                    onChange={handlePaymentMethodChange}
                    sx={{ gap: 1 }}
                  >
                    <FormControlLabel
                      value="wallet"
                      control={<Radio />}
                      label={
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Typography>Ví của bạn</Typography>
                          <Typography
                            sx={{
                              ml: 1,
                              color:
                                Number(profile?.balance || 0) < selectedPlan.price
                                  ? "red"
                                  : "green",
                            }}
                          >
                            (Số dư: {Number(profile?.balance || 0).toLocaleString()}
                            {CURRENCY_UNIT})
                          </Typography>
                        </Box>
                      }
                    />
                    {/* <FormControlLabel
                      value="transfer"
                      control={<Radio />}
                      label="Chuyển khoản ngân hàng"
                    /> */}
                  </RadioGroup>
                  {paymentMethod === "wallet" &&
                    Number(profile?.balance || 0) !== null &&
                    Number(profile?.balance || 0) < selectedPlan.price && (
                      <Typography sx={{ color: "red", fontSize: "0.9rem", mt: 1 }}>
                        Số dư ví không đủ
                      </Typography>
                    )}
                  {paymentMethod === "transfer" && (
                    <Typography sx={{ fontSize: "0.9rem", color: "text.secondary", mt: 1 }}>
                      Vui lòng chuyển khoản đến tài khoản: [Thông tin ngân hàng]
                    </Typography>
                  )}
                </FormControl>
              </>
            )}
          </DialogContent>
          <DialogActions sx={{ p: 0, justifyContent: "center", gap: 2 }}>
            {isActionSuccess ? (
              <Button
                onClick={() => router.push(paths.settings.service)}
                variant="contained"
                sx={{
                  borderRadius: "50px",
                  textTransform: "none",
                  fontSize: "0.95rem",
                  px: 3,
                  py: 1,
                  backgroundColor: "#6366F1",
                  "&:hover": {
                    backgroundColor: "#5254cc",
                  },
                }}
              >
                Quản lý gói dịch vụ
              </Button>
            ) : (
              <>
                <Button
                  onClick={handleCloseDialog}
                  variant="outlined"
                  sx={{
                    borderRadius: "50px",
                    textTransform: "none",
                    fontSize: "0.95rem",
                    px: 3,
                    py: 1,
                    borderColor: "#e0e0e0",
                    color: "#6366F1",
                    "&:hover": {
                      borderColor: "#d0d0d0",
                      backgroundColor: "rgba(99, 102, 241, 0.04)",
                    },
                  }}
                >
                  Đóng
                </Button>
                <Button
                  onClick={handleActionPackage}
                  variant="contained"
                  disabled={
                    isLoadingDialog ||
                    (paymentMethod === "wallet" &&
                      Number(profile?.balance || 0) !== null &&
                      Number(profile?.balance || 0) < selectedPlan.price)
                  }
                  sx={{
                    borderRadius: "50px",
                    textTransform: "none",
                    fontSize: "0.95rem",
                    px: 3,
                    py: 1,
                    backgroundColor: "#6366F1",
                    "&:hover": {
                      backgroundColor: "#5254cc",
                    },
                  }}
                >
                  {isUpgradeMode ? "Nâng cấp ngay" : "Mua ngay"}
                </Button>
              </>
            )}
          </DialogActions>
        </Dialog>
      )}
    </Container>
  );
};

export default PackageManager;
