import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TablePagination,
  Typography,
  TextField,
  Tabs,
  Tab,
  IconButton,
  Stack,
  Checkbox,
  Button,
  Popover,
  Tooltip,
  CircularProgress,
  InputAdornment,
  Fade,
  Alert,
  Skeleton,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import VisibilityIcon from "@mui/icons-material/Visibility";
import { useVoucher } from "@/src/api/hooks/voucher/use-voucher";
import { useStoreId } from "@/src/hooks/use-store-id";
import { useSnackbar } from "@/src/hooks/use-snackbar";
import {
  TYPE_DISTRIBUTION,
  VOUCHER_STATUS,
  VOUCHER_STATUS_LABLE,
  VOUCHER_TYPE,
} from "@/src/api/types/voucher.type";
import { useRouter } from "next/router";
import { paths } from "@/src/paths";
import TitleDialog from "@/src/components/dialog/TitleDialog";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM } from "@/src/constants/constant";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import {
  effectiveDateVoucher,
  releaseTypeText,
} from "@/src/components/vouchers/promotions/PromotionSummaryBox";
import { StatusBadge } from "../list";
import FileDownloadOutlinedIcon from "@mui/icons-material/FileDownloadOutlined";
import { QRCode } from "zmp-qrcode";
import { useDebounce } from "@/src/hooks/use-debounce";
import { StorageService } from "nextjs-api-lib";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import { logger } from "@/src/utils/logger";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { Search } from "@mui/icons-material";
import { useAppSelector } from "@/src/redux/hooks";
import {
  VoucherTableSkeleton,
  VoucherEmptyState,
} from "@/src/components/vouchers/VoucherTableSkeleton";
import { formatTruncatedText } from "@/src/utils/format";

interface SingleCodeVoucherListProps {
  voucherTypes: string[];
  codeType: string;
  onEditVoucher?: (voucherType: string, voucherId: string) => void;
  selected: string[];
  setSelected: (selected: string[]) => void;
  refreshKey?: number;
  onAddNew?: () => void;
}

const SingleCodeVoucherList = ({
  voucherTypes,
  codeType,
  onEditVoucher,
  selected,
  setSelected,
  refreshKey,
  onAddNew,
}: SingleCodeVoucherListProps) => {
  const pathname = usePathname();
  const router = useRouter();
  const storeId = useStoreId();
  const { listVoucher, deleteVoucher, listVoucherDetail, exportPdf } = useVoucher();
  const snackbar = useSnackbar();
  const { permissions } = useAllPermissions();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [vouchers, setVouchers] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [searchText, setSearchText] = useState("");
  const [searchTabIndex, setSearchTabIndex] = useState("All");
  const [isDeleteMany, setIsDeleteMany] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState({ open: false, voucher: null });
  const [viewDetailsDialog, setViewDetailsDialog] = useState({ open: false, voucher: null });
  const debouncedSearchValue = useDebounce(searchText, 500);
  const [voucherDetails, setVoucherDetails] = useState([]);
  const [detailsPage, setDetailsPage] = useState(0);
  const [detailsRowsPerPage, setDetailsRowsPerPage] = useState(10);
  const [detailsTotalCount, setDetailsTotalCount] = useState(0);
  const [exportLoading, setExportLoading] = useState(false);
  const currentShop = useAppSelector((state) => state.shop.currentShop);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const searchTabs = [
    { key: "All", title: "Tất cả" },
    { key: VOUCHER_STATUS.ACTIVED, title: VOUCHER_STATUS_LABLE[VOUCHER_STATUS.ACTIVED] },
    { key: VOUCHER_STATUS.INACTIVED, title: VOUCHER_STATUS_LABLE[VOUCHER_STATUS.INACTIVED] },
    { key: VOUCHER_STATUS.EXPIRED, title: VOUCHER_STATUS_LABLE[VOUCHER_STATUS.EXPIRED] },
  ];

  const isGranted = (url, permission) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  const fetchVoucherList = async (currentPage, pageSize, searchQuery, searchType) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await listVoucher({
        skip: currentPage,
        limit: pageSize,
        shopId: storeId,
        search: searchQuery,
        type: voucherTypes,
        statusVoucher: searchType === "All" ? null : searchType,
        codeType: codeType,
      });

      if (response.data.result) {
        setVouchers(response.data.result.result);
        setTotalCount(response.data.result.total || 0);
      } else {
        setError(response.data.message || "Có lỗi xảy ra khi tải dữ liệu");
        snackbar.error(response.data.message);
      }
    } catch (err) {
      setError("Không thể tải danh sách voucher");
      snackbar.error("Không thể tải danh sách voucher");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchVoucherDetails = async (voucherId, page = 0, rowsPerPage = 10) => {
    const response = await listVoucherDetail({
      voucherId,
      skip: page,
      limit: rowsPerPage,
    });
    if (response?.data?.result) {
      setVoucherDetails(response.data.result.result);
      setDetailsTotalCount(response.data.result.total || 0);
    } else {
      snackbar.error(response?.data?.message || "Không thể tải chi tiết voucher");
    }
  };

  useEffect(() => {
    if (storeId) {
      fetchVoucherList(page, rowsPerPage, debouncedSearchValue, searchTabIndex);
    }
  }, [page, rowsPerPage, debouncedSearchValue, storeId, searchTabIndex, refreshKey]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
    setSelected([]);
  };
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchTabChange = (event, newTabIndex) => {
    setPage(0);
    setSearchTabIndex(newTabIndex);
    setSelected([]);
  };

  const handleSelectAll = (event) => {
    if (event.target.checked) {
      setSelected(vouchers.map((c) => c.voucherId));
    } else {
      setSelected([]);
    }
  };

  const handleSelectOne = (event, id) => {
    if (event.target.checked) {
      setSelected([...selected, id]);
    } else {
      setSelected(selected.filter((item) => item !== id));
    }
  };

  const handleOpenConfirmDialog = (voucher) => {
    setConfirmDialog({ open: true, voucher });
  };

  const handleCloseConfirmDialog = () => {
    setConfirmDialog({ open: false, voucher: null });
  };

  const handleConfirmDelete = async () => {
    if (confirmDialog.voucher) {
      const response = await deleteVoucher([confirmDialog.voucher.voucherId]);
      if (response?.data.result) {
        snackbar.success("Xóa voucher thành công");
        fetchVoucherList(page, rowsPerPage, debouncedSearchValue, searchTabIndex);
      } else {
        snackbar.error(response.data.message);
      }
    }
    handleCloseConfirmDialog();
  };

  const handleConfirmDeleteMany = async () => {
    if (selected.length === 0) return;

    const response = await deleteVoucher(selected);
    if (response?.data?.result) {
      snackbar.success(`Xóa ${selected.length} voucher thành công`);
      fetchVoucherList(page, rowsPerPage, debouncedSearchValue, searchTabIndex);
      setSelected([]);
      setIsDeleteMany(false);
    } else {
      snackbar.error(response?.data?.message || "Xóa voucher thất bại");
    }
  };

  const handleCloseDeleteMany = () => {
    setIsDeleteMany(false);
  };

  const handleEditVoucher = (voucherType, voucherId) => {
    if (onEditVoucher) {
      onEditVoucher(voucherType, voucherId);
    } else {
      if (voucherType === VOUCHER_TYPE.PROMOTION) {
        router.push(`${paths.marketing.vouchers.promotionDetail}?id=${voucherId}`);
      } else if (voucherType === VOUCHER_TYPE.TRANSPORT) {
        router.push(`${paths.marketing.vouchers.transportDetail}?id=${voucherId}`);
      } else {
        router.push(`${paths.marketing.vouchers.customVoucherDetail}?id=${voucherId}`);
      }
    }
  };

  const handleOpenViewDetails = (voucher) => {
    setViewDetailsDialog({ open: true, voucher });
    setDetailsPage(0);
    setDetailsRowsPerPage(10);
    fetchVoucherDetails(voucher.voucherId, 0, 10);
  };

  const handleCloseViewDetails = () => {
    setViewDetailsDialog({ open: false, voucher: null });
  };

  const handleDetailsPageChange = (event, newPage) => {
    setDetailsPage(newPage);
    fetchVoucherDetails(viewDetailsDialog.voucher.voucherId, newPage, detailsRowsPerPage);
  };

  const handleDetailsRowsPerPageChange = (event) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setDetailsRowsPerPage(newRowsPerPage);
    setDetailsPage(0);
    fetchVoucherDetails(viewDetailsDialog.voucher.voucherId, 0, newRowsPerPage);
  };

  const handleExportPdf = async () => {
    if (!viewDetailsDialog.voucher) return;
    setExportLoading(true);
    try {
      const response = await exportPdf(viewDetailsDialog.voucher.voucherId);
      console.log(response);

      if (response?.data?.data?.pdfExport?.link) {
        // Tải file về bằng fetch blob để không mở tab mới
        const fileUrl = response.data.data.pdfExport.link;
        const fileName = fileUrl.split("/").pop() || "VoucherCodes.pdf";
        const res = await fetch(fileUrl);
        const blob = await res.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        snackbar.success("Xuất PDF thành công");
      } else {
        snackbar.error(response?.data?.message || "Xuất PDF thất bại");
      }
    } catch (err) {
      snackbar.error("Xuất PDF thất bại");
      console.error("Lỗi export PDF:", err);
    } finally {
      setExportLoading(false);
    }
  };

  return (
    <Box>
      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", md: "row" },
            justifyContent: "space-between",
            alignItems: { xs: "flex-start", md: "center" },
            gap: { xs: 2, md: 0 },
            mb: 3,
          }}
        >
          <Box sx={{ width: { xs: "100%", md: "auto" }, overflow: "hidden" }}>
            <Tabs
              value={searchTabIndex}
              onChange={handleSearchTabChange}
              sx={{
                "& .MuiTabs-indicator": {
                  color: "#2654FE",
                },
                "& .MuiTabs-scroller": {
                  "& .MuiTabs-flexContainer": {
                    gap: { xs: "8px", md: "0px" },
                  },
                },
              }}
              variant="scrollable"
              scrollButtons={false}
            >
              {searchTabs.map((tab) => (
                <Tab
                  sx={{
                    textTransform: "none",
                    color: "black",
                    minWidth: { xs: "80px", md: "60px" },
                    fontSize: { xs: "14px", md: "14px" },
                    padding: { xs: "6px 8px", md: "6px 12px" },
                    minHeight: "40px",
                    "&.Mui-selected": {
                      color: "#2654FE",
                    },
                  }}
                  key={tab.key}
                  value={tab.key}
                  label={tab.title}
                />
              ))}
            </Tabs>
          </Box>

          <Box sx={{ width: { xs: "100%", md: "300px" } }}>
            <TextField
              placeholder="Tìm kiếm mã giảm giá"
              variant="outlined"
              size="small"
              fullWidth
              onChange={(e) => {
                setPage(0);
                setSearchText(e.target.value);
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: "8px",
                  height: "40px",
                },
              }}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Box>
        </Box>

        <Box sx={{ width: "100%", overflowX: "auto" }}>
          <Table
            sx={{
              width: "max-content",
              minWidth: "100%",
              tableLayout: "auto",
            }}
          >
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox" sx={{ width: "48px", minWidth: "48px" }}>
                  <Checkbox
                    checked={vouchers.length > 0 && selected.length === vouchers.length}
                    indeterminate={selected.length > 0 && selected.length < vouchers.length}
                    onChange={handleSelectAll}
                  />
                </TableCell>
                <TableCell sx={{ whiteSpace: "nowrap", minWidth: "60px" }}>STT</TableCell>
                <TableCell sx={{ whiteSpace: "nowrap", minWidth: "120px" }}>Voucher</TableCell>
                <TableCell sx={{ whiteSpace: "nowrap", minWidth: "120px" }}>
                  Hình thức phát hành
                </TableCell>
                <TableCell sx={{ whiteSpace: "nowrap", minWidth: "150px" }}>Loại voucher</TableCell>
                <TableCell sx={{ whiteSpace: "nowrap", minWidth: "100px" }}>Trạng thái</TableCell>
                <TableCell sx={{ whiteSpace: "nowrap", minWidth: "100px" }}>Đã sử dụng</TableCell>
                <TableCell sx={{ whiteSpace: "nowrap", minWidth: "100px" }}>Đã phát</TableCell>
                <TableCell sx={{ whiteSpace: "nowrap", minWidth: "150px" }}>
                  Ngày có hiệu lực
                </TableCell>
                <TableCell
                  sx={{
                    whiteSpace: "nowrap",
                    minWidth: "120px",
                    position: "sticky",
                    right: 0,
                    bottom: 0,
                    backgroundColor: "#fff",
                    zIndex: 3,
                    boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                    padding: { xs: "16px 4px", sm: "20px 16px" },
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    // width: { xs: "70px", sm: "90px" },
                  }}
                >
                  Quản lý
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading
                ? // Loading skeleton rows
                  Array.from({ length: rowsPerPage }).map((_, index) => (
                    <TableRow key={`skeleton-${index}`}>
                      <TableCell padding="checkbox">
                        <Skeleton variant="rectangular" width={20} height={20} />
                      </TableCell>
                      <TableCell>
                        <Skeleton variant="text" width={30} />
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={2} alignItems="center">
                          <Skeleton
                            variant="rectangular"
                            width={50}
                            height={50}
                            sx={{ borderRadius: 1 }}
                          />
                          <Stack spacing={0.5}>
                            <Skeleton variant="text" width={150} />
                            <Skeleton variant="text" width={120} />
                          </Stack>
                        </Stack>
                      </TableCell>
                      <TableCell>
                        <Skeleton variant="text" width={120} />
                      </TableCell>
                      <TableCell>
                        <Skeleton variant="text" width={100} />
                      </TableCell>
                      <TableCell>
                        <Skeleton
                          variant="rectangular"
                          width={70}
                          height={24}
                          sx={{ borderRadius: 1 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Skeleton variant="text" width={60} />
                      </TableCell>
                      <TableCell>
                        <Skeleton variant="text" width={60} />
                      </TableCell>
                      <TableCell>
                        <Skeleton variant="text" width={120} />
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={1}>
                          <Skeleton variant="circular" width={32} height={32} />
                          <Skeleton variant="circular" width={32} height={32} />
                          <Skeleton variant="circular" width={32} height={32} />
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))
                : vouchers.length > 0
                ? vouchers.map((c, index) => (
                    <TableRow key={c.voucherId}>
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={selected.includes(c.voucherId)}
                          onChange={(event) => handleSelectOne(event, c.voucherId)}
                        />
                      </TableCell>
                      <TableCell sx={{ whiteSpace: "nowrap" }}>
                        {page * rowsPerPage + index + 1}
                      </TableCell>
                      <TableCell>
                        <Box display={"flex"} alignItems={"center"} gap={2}>
                          <img
                            style={{ width: 50, height: 50, objectFit: "cover", borderRadius: 5 }}
                            src={c.image?.link || currentShop.shopLogo.link}
                            alt={c.voucherName}
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = currentShop.shopLogo.link;
                            }}
                          />
                          <Box>
                            <TruncatedText
                              {...formatTruncatedText({
                                text: c.voucherName,
                                isGranted: isGranted(pathname, PERMISSION_TYPE_ENUM.Edit),
                                openInNewTab: false,
                                fontSize: 18,
                                actionNeed: () => handleEditVoucher(c.voucherType, c.voucherId),
                                width: "300px",
                              })}
                            />

                            <TruncatedText
                              typographyProps={{ fontSize: 14, fontWeight: 400, color: "#424242" }}
                              text={
                                c.voucherDetailCount < c.quantity
                                  ? `Đang phát hành (${c.voucherDetailCount}/${c.quantity} mã)`
                                  : `Đã phát hành (${c.voucherDetailCount}/${c.quantity} mã)`
                              }
                            />
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ whiteSpace: "nowrap" }}>
                        {c.distributionType == TYPE_DISTRIBUTION.IMMEDIATE
                          ? "Phát hành ngay lập tức"
                          : "Phát hành sau"}
                      </TableCell>
                      <TableCell sx={{ whiteSpace: "nowrap" }}>
                        <Typography>
                          {c.voucherType === VOUCHER_TYPE.PROMOTION
                            ? "Giảm giá sản phẩm"
                            : c.voucherType === VOUCHER_TYPE.TRANSPORT
                            ? "Giảm giá phí vận chuyển"
                            : c.voucherType === VOUCHER_TYPE.CUSTOM
                            ? "Khuyến mãi tuỷ chỉnh"
                            : ""}
                        </Typography>
                        <Typography variant="subtitle2" color="text.secondary">
                          {releaseTypeText(c.releaseType)}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ whiteSpace: "nowrap" }}>
                        <StatusBadge status={c.status} />
                      </TableCell>
                      <TableCell sx={{ whiteSpace: "nowrap" }}>{c.quantityUsed}</TableCell>
                      <TableCell sx={{ whiteSpace: "nowrap" }}>
                        {c.quantity - (c.remainingStock || 0)}
                      </TableCell>
                      <TableCell sx={{ whiteSpace: "nowrap" }}>{effectiveDateVoucher(c)}</TableCell>
                      <TableCell
                        sx={{
                          whiteSpace: "nowrap",
                          display: "flex",
                          flexDirection: "column",
                          position: "sticky",
                          right: 0,
                          backgroundColor: "#fff",
                          zIndex: 2,
                          padding: "25px 16px",
                          boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                        }}
                      >
                        <Stack direction="row" spacing={1}>
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleOpenViewDetails(c)}
                          >
                            <VisibilityIcon sx={{ fontSize: 20 }} />
                          </IconButton>
                          {isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? (
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleEditVoucher(c.voucherType, c.voucherId)}
                            >
                              <EditIcon sx={{ fontSize: 20 }} />
                            </IconButton>
                          ) : (
                            <Tooltip title="Bạn không có quyền sửa">
                              <span>
                                <IconButton size="small" disabled={true}>
                                  <EditIcon sx={{ fontSize: 20 }} />
                                </IconButton>
                              </span>
                            </Tooltip>
                          )}
                          {isGranted(pathname, PERMISSION_TYPE_ENUM.Add) ? (
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleOpenConfirmDialog(c)}
                            >
                              <DeleteIcon sx={{ fontSize: 20 }} />
                            </IconButton>
                          ) : (
                            <Tooltip title="Bạn không có quyền xoá">
                              <span>
                                <IconButton size="small" disabled={true}>
                                  <DeleteIcon sx={{ fontSize: 20 }} />
                                </IconButton>
                              </span>
                            </Tooltip>
                          )}
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))
                : null}
            </TableBody>
          </Table>

          {/* Empty State */}
          {!isLoading && vouchers.length === 0 && (
            <VoucherEmptyState
              title="Chưa có voucher một mã nào"
              description="Tạo voucher một mã đầu tiên để khách hàng có thể sử dụng mã riêng biệt"
              action={
                <Button
                  variant="contained"
                  color="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    if (onAddNew) {
                      onAddNew();
                    } else {
                      router.push(paths.marketing.vouchers.newPromotion);
                    }
                  }}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1.5,
                    fontWeight: 600,
                  }}
                >
                  Tạo voucher
                </Button>
              }
            />
          )}
        </Box>

        {/* Pagination - only show when there are vouchers */}
        {vouchers.length > 0 && (
          <TablePagination
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={rowPerPageOptionsDefault}
            labelRowsPerPage="Số dòng mỗi trang"
            sx={{
              borderTop: 1,
              borderColor: "divider",
              "& .MuiTablePagination-toolbar": {
                px: 2,
              },
            }}
          />
        )}
      </Box>

      <TitleDialog
        title="Xóa voucher"
        open={confirmDialog.open}
        handleClose={handleCloseConfirmDialog}
        handleSubmit={handleConfirmDelete}
        submitBtnTitle="Xác nhận"
        color="error"
      >
        <Box>
          <Typography>Bạn có chắc muốn xóa voucher này?</Typography>
        </Box>
      </TitleDialog>

      <TitleDialog
        title="Xóa voucher"
        open={isDeleteMany}
        handleClose={handleCloseDeleteMany}
        handleSubmit={handleConfirmDeleteMany}
        submitBtnTitle="Xác nhận"
        color="error"
      >
        <Box>
          <Typography>
            Bạn có chắc muốn xóa {selected.length > 0 && `${selected.length}`} voucher này?
          </Typography>
        </Box>
      </TitleDialog>

      <TitleDialog
        title="Chi tiết mã giảm giá"
        open={viewDetailsDialog.open}
        handleClose={handleCloseViewDetails}
        showActionDialog={false}
        maxWidth="lg"
      >
        <Box sx={{ width: "100%" }}>
          {viewDetailsDialog?.voucher?.pdfExport && (
            <Box sx={{ mb: 2, display: "flex", justifyContent: "flex-end" }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<FileDownloadOutlinedIcon />}
                disabled={!viewDetailsDialog.voucher || exportLoading}
                onClick={handleExportPdf}
              >
                {exportLoading ? <CircularProgress size={20} color="inherit" /> : "Xuất file PDF"}
              </Button>
            </Box>
          )}
          <Box sx={{ maxHeight: 500, overflow: "auto" }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>STT</TableCell>
                  <TableCell>Mã giảm giá</TableCell>
                  <TableCell>QR Code (Hình ảnh)</TableCell>
                  {viewDetailsDialog.voucher?.distributionType == TYPE_DISTRIBUTION.ON_RECEIVE && (
                    <TableCell>Người dùng</TableCell>
                  )}
                  <TableCell>Đã sử dụng</TableCell>
                  <TableCell>Ngày tạo</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {voucherDetails?.map((detail, index) => (
                  <TableRow key={detail.id}>
                    <TableCell>{detailsPage * detailsRowsPerPage + index + 1}</TableCell>
                    <TableCell>{detail.voucherCode}</TableCell>
                    <TableCell>
                      <div
                        style={{
                          display: "inline-block",
                          background: "#fff",
                          padding: 8,
                          borderRadius: 8,
                        }}
                      >
                        <QRCode value={detail.voucherCodeLink} size={100} />
                      </div>
                    </TableCell>
                    {viewDetailsDialog.voucher?.distributionType ==
                      TYPE_DISTRIBUTION.ON_RECEIVE && (
                      <TableCell>
                        <StatusBadge status={detail?.user?.fullname} />
                        <br />
                        <Box mb={1}></Box>
                        <StatusBadge status={detail?.user?.phoneNumber} />
                      </TableCell>
                    )}
                    <TableCell>
                      {detail.user
                        ? Math.max(viewDetailsDialog?.voucher?.maxUsagePerUser - detail.numUse, 0)
                        : 0}{" "}
                      / {viewDetailsDialog?.voucher?.maxUsagePerUser}
                    </TableCell>
                    <TableCell>{new Date(detail.createdDate).toLocaleString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Box>
          <TablePagination
            component="div"
            count={detailsTotalCount}
            page={detailsPage}
            onPageChange={handleDetailsPageChange}
            rowsPerPage={detailsRowsPerPage}
            onRowsPerPageChange={handleDetailsRowsPerPageChange}
            rowsPerPageOptions={rowPerPageOptionsDefault}
            labelRowsPerPage="Số dòng mỗi trang"
            sx={{ mt: 2 }}
          />
        </Box>
      </TitleDialog>
    </Box>
  );
};

export default SingleCodeVoucherList;
