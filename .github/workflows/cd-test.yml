name: Build and Deploy Next.js to Server Testing

on:
  push:
    branches:
      - develop
  workflow_dispatch:

jobs:
  build:
    name: Build
    # if: github.ref == 'refs/heads/deploy'
    runs-on: [self-hosted, dev]
    env:
      YARN_CACHE_FOLDER: /opt/yarn-cache
    steps:
      - name: Prepare local yarn cache (first run will create)
        run: |
          sudo mkdir -p /opt/yarn-cache
          sudo chown -R "$USER:$USER" /opt/yarn-cache
      - uses: actions/checkout@v4
        with:
          clean: false
          fetch-depth: 0

      - name: Export PATH
        run: echo "$HOME/.yarn/bin:$HOME/.config/yarn/global/node_modules/.bin" >> $GITHUB_PATH

      - name: Setup Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install deps (reuse existing node_modules)
        run: |
          yarn install --frozen-lockfile --prefer-offline

      - name: Build
        run: |
          echo "timestamp=$(date '+%d/%m/%Y %H:%M:%S')" >> $GITHUB_ENV
          yarn build:development
          sudo cp -r build/. /var/www/admin-partner/build

      - name: Send noti
        run: |
          curl --location 'https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage' \
          --header 'Content-Type: application/x-www-form-urlencoded' \
          --data-urlencode 'chat_id=-4520692772' \
          --data-urlencode 'text=[${{ env.timestamp }}] Build AdminPartner Dev success - ${{ github.event.head_commit.message }}'
