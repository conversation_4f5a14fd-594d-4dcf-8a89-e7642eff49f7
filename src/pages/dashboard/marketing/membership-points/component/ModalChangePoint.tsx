import { Close } from "@mui/icons-material";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import React from "react";
import { AdjustPointState } from "./UserPointsTab";
import { formatMoney } from "@/src/utils/format-money";

interface ModalChangePointProp {
  openDialog: boolean;
  handleCloseDialog?: any;
  user?: any;
  adjustPointData: AdjustPointState;
  setAdjustPointData: React.Dispatch<React.SetStateAction<AdjustPointState>>;
  handleUpdatePoints?: any;
}
const ModalChangePoint: React.FC<ModalChangePointProp> = ({
  openDialog,
  handleCloseDialog,
  user,
  adjustPointData,
  setAdjustPointData,
  handleUpdatePoints,
}) => {
  return (
    <>
      <Dialog open={openDialog} onClose={handleCloseDialog} fullWidth maxWidth="xs">
        <DialogTitle
          sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}
        >
          Điều chỉnh điểm
          <IconButton onClick={handleCloseDialog}>
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 0 }}>
            <Typography
              sx={{ background: "#f7f8fa", padding: "10px 20px", borderRadius: "5px", mb: 2.5 }}
            >
              Điểm hiện có: {formatMoney(user?.points || 0)} điểm
            </Typography>
            <Typography sx={{ display: "flex", fontSize: "16px", fontWeight: 500, mb: 0.2 }}>
              Loại điều chỉnh <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
            </Typography>
            <RadioGroup
              value={adjustPointData.adjustType}
              onChange={(e) => {
                setAdjustPointData((prev) => ({
                  ...prev,
                  adjustType: e.target.value as "add" | "subtract",
                  points: "",
                }));
              }}
              row
              sx={{ mb: 2 }}
            >
              <FormControlLabel value="add" control={<Radio />} label="Cộng điểm" />
              <FormControlLabel value="subtract" control={<Radio />} label="Trừ điểm" />
            </RadioGroup>

            <Typography sx={{ display: "flex", fontSize: "16px", fontWeight: 500, mb: 0.2 }}>
              Số điểm <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
            </Typography>
            <TextField
              fullWidth
              type="number"
              value={adjustPointData?.points}
              onChange={(e) => {
                const value = Number(e.target.value);
                const currentPoints = user?.points || 0;

                setAdjustPointData((prev) => ({
                  ...prev,
                  points:
                    prev.adjustType === "subtract"
                      ? value <= currentPoints
                        ? value.toString()
                        : prev.points
                      : value.toString(),
                }));
              }}
              onKeyDown={(e) => {
                if (e.key === "." || e.key === "e" || e.key === "E") {
                  e.preventDefault();
                }
              }}
              onWheel={(e) => e.target instanceof HTMLElement && e.target.blur()}
              sx={{ mb: 2 }}
              placeholder={`Nhập số điểm cần ${
                adjustPointData?.adjustType === "add" ? "cộng" : "trừ"
              }`}
              inputProps={{
                min: 0,
                max: adjustPointData?.adjustType === "subtract" ? user?.points || 0 : undefined,
              }}
            />

            <Typography sx={{ display: "flex", fontSize: "16px", fontWeight: 500, mb: 0.2 }}>
              Lý do <Typography sx={{ color: "red", ml: 0.5 }}>*</Typography>
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={3}
              placeholder="Lý do điều chỉnh"
              value={adjustPointData?.reason}
              onChange={(e) => {
                setAdjustPointData((prev) => ({
                  ...prev,
                  reason: e.target.value,
                }));
              }}
              inputProps={{ maxLength: 100 }}
              error={!adjustPointData?.reason}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ marginRight: 2, marginBottom: 1 }}>
          <Button
            onClick={handleCloseDialog}
            variant="outlined"
            disabled={adjustPointData?.isSubmitting}
          >
            Hủy bỏ
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleUpdatePoints}
            disabled={
              adjustPointData?.isSubmitting || !adjustPointData?.points || !adjustPointData?.reason
            }
          >
            {adjustPointData?.isSubmitting ? "Đang xử lý..." : "Lưu"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ModalChangePoint;
