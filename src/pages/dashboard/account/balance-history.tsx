import { useBalanceLog } from "@/src/api/hooks/balance-log/balance-log";
import { useUser } from "@/src/api/hooks/user/use-user";
import {
  BalanceLogRequestDto,
  IBalanceLogDto,
} from "@/src/api/services/balance-log/balance-log.service";
import { useDebounce } from "@/src/hooks/use-debounce";
import { useAppSelector } from "@/src/redux/hooks";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { Padding, Search } from "@mui/icons-material";
import {
  Box,
  Button,
  Card,
  Dialog,
  FormControl,
  Input,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import DashboardLayout from "@/src/layouts/dashboard";
import { fontSize } from "@mui/system";
import useSnackbar from "@/src/hooks/use-snackbar";
import { CURRENCY_UNIT } from "@/src/constants/constant";
import { formatMoney } from "@/src/utils/format-money";

export function getBalanceHistoryVietnamese(type: string): string {
  switch (type) {
    case "Deposit":
      return "Nạp tiền";
    case "Deduction":
      return "Khấu trừ";
    case "Refund":
      return "Hoàn tiền";
    case "Adjustment":
      return "Điều chỉnh số dư";
    case "Chargeback":
      return "Bồi hoàn do tranh chấp";
    default:
      return "Không xác định";
  }
}

const BalanceHistory = () => {
  const { getBalanceHistory } = useBalanceLog();
  const { profile } = useAppSelector((state) => state.profile);
  const snackbar = useSnackbar();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [filterStatus, setFilterStatus] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [openTopUpDialog, setOpenTopUpDialog] = useState(false);
  const [topUpAmount, setTopUpAmount] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("banking");
  const [listBalanceHistory, setListBalanceHistory] = useState<IBalanceLogDto[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [typeFilter, setTypeFilter] = useState<string>("");

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };
  const debouncedSearchValue = useDebounce(searchTerm, 500);

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleTopUp = () => {
    if (!topUpAmount) return;
    setOpenTopUpDialog(false);
    setTopUpAmount("");
    alert(`Nạp ${formatCurrency(parseInt(topUpAmount))} qua ${paymentMethod} thành công!`);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "completed":
        return "#4caf50";
      case "pending":
        return "#ff9800";
      case "failed":
        return "#f44336";
      default:
        return "#757575";
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case "completed":
        return "Thành công";
      case "pending":
        return "Đang xử lý";
      case "failed":
        return "Thất bại";
      default:
        return status;
    }
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case "topup":
        return "📈";
      case "refund":
        return "💰";
      case "purchase":
        return "📉";
      default:
        return "💳";
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(Math.abs(amount));
  };

  const styles = {
    container: {
      // padding: "24px",
      minWidth: "1200px",
      margin: "0 auto",
      backgroundColor: "#f5f5f5",
      minHeight: "100vh",
      fontFamily: "'Roboto', 'Helvetica', 'Arial', sans-serif",
    },
    header: {
      marginBottom: "32px",
    },
    title: {
      fontSize: "2rem",
      fontWeight: "700",
      marginBottom: "8px",
      color: "#1976d2",
    },
    subtitle: {
      color: "#666",
      fontSize: "1rem",
    },
    balanceSection: {
      display: "grid",
      gridTemplateColumns: "2fr 1fr",
      gap: "24px",
      marginBottom: "32px",
    },
    balanceCard: {
      background: "linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)",
      borderRadius: "12px",
      padding: "24px",
      color: "white",
      boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
    },
    balanceInfo: {
      display: "flex",
      alignItems: "center",
    },
    balanceIcon: {
      width: "48px",
      height: "48px",
      background: "rgba(255,255,255,0.2)",
      borderRadius: "50%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      marginRight: "16px",
      fontSize: "20px",
    },
    balanceText: {
      fontSize: "1.2rem",
      marginBottom: "8px",
      fontWeight: "500",
    },
    balanceAmount: {
      fontSize: "2rem",
      fontWeight: "700",
    },
    topUpButton: {
      background: "linear-gradient(45deg, #f44336 30%, #ff9800 90%)",
      border: "none",
      borderRadius: "12px",
      color: "white",
      fontSize: "1.1rem",
      fontWeight: "600",
      cursor: "pointer",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      gap: "8px",
      height: "100%",
      minHeight: "80px",
      boxShadow: "0 3px 5px 2px rgba(244, 67, 54, .3)",
      transition: "transform 0.2s, box-shadow 0.2s",
    },
    card: {
      background: "white",
      borderRadius: "12px",
      padding: "24px",
      marginBottom: "24px",
      boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
    },
    filtersGrid: {
      display: "grid",
      gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
      gap: "16px",
      alignItems: "center",
    },
    input: {
      padding: "12px 16px",
      border: "2px solid #e0e0e0",
      borderRadius: "8px",
      fontSize: "14px",
      width: "100%",
      transition: "border-color 0.2s",
    },
    select: {
      padding: "12px 16px",
      border: "2px solid #e0e0e0",
      borderRadius: "8px",
      fontSize: "14px",
      width: "100%",
      transition: "border-color 0.2s",
      backgroundColor: "white",
    },
    filterButtons: {
      display: "flex",
      gap: "8px",
    },
    filterBtn: {
      padding: "12px 16px",
      border: "2px solid #e0e0e0",
      background: "white",
      borderRadius: "8px",
      cursor: "pointer",
      transition: "all 0.2s",
    },
    table: {
      width: "100%",
      borderCollapse: "collapse",
      backgroundColor: "white",
    },
    tableHeader: {
      backgroundColor: "#f5f5f5",
      padding: "16px",
      textAlign: "left",
      fontWeight: "600",
      color: "#555",
      borderBottom: "1px solid #e0e0e0",
      fontSize: "16px",
    },
    tableCell: {
      padding: "16px",
      borderBottom: "1px solid #f0f0f0",
      fontSize: "16px",
    },
    transactionId: {
      fontWeight: "600",
      color: "#1976d2",
    },
    transactionIcon: {
      width: "32px",
      height: "32px",
      borderRadius: "50%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      fontSize: "16px",
      backgroundColor: "#f5f5f5",
    },
    amountPositive: {
      color: "#4caf50",
      fontWeight: "600",
    },
    amountNegative: {
      color: "#f44336",
      fontWeight: "600",
    },
    statusChip: {
      padding: "4px 12px",
      borderRadius: "16px",
      fontSize: "12px",
      fontWeight: "500",
      color: "white",
    },
    methodChip: {
      padding: "4px 12px",
      border: "1px solid #e0e0e0",
      borderRadius: "16px",
      fontSize: "12px",
      backgroundColor: "#f5f5f5",
    },
    pagination: {
      padding: "16px 24px",
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      borderTop: "1px solid #e0e0e0",
    },
    paginationInfo: {
      color: "#666",
      fontSize: "14px",
    },
    paginationControls: {
      display: "flex",
      gap: "8px",
    },
    paginationBtn: {
      padding: "8px 12px",
      border: "1px solid #e0e0e0",
      background: "white",
      borderRadius: "4px",
      cursor: "pointer",
      transition: "all 0.2s",
    },
    modal: {
      position: "fixed",
      top: 0,
      left: 0,
      width: "100%",
      height: "100%",
      backgroundColor: "rgba(0,0,0,0.5)",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      zIndex: 1000,
    },
    modalContent: {
      backgroundColor: "white",
      borderRadius: "12px",
      width: "90%",
      maxWidth: "500px",
      boxShadow: "0 8px 32px rgba(0,0,0,0.2)",
    },
    modalHeader: {
      padding: "24px 24px 16px",
      borderBottom: "1px solid #e0e0e0",
    },
    modalTitle: {
      display: "flex",
      alignItems: "center",
      gap: "12px",
      fontSize: "1.5rem",
      fontWeight: "600",
      margin: 0,
    },
    modalBody: {
      padding: "24px",
    },
    formGroup: {
      marginBottom: "20px",
    },
    label: {
      display: "block",
      marginBottom: "8px",
      fontWeight: "500",
      color: "#333",
    },
    modalActions: {
      padding: "16px 24px 24px",
      display: "flex",
      gap: "12px",
      justifyContent: "flex-end",
    },
    btn: {
      padding: "12px 24px",
      border: "none",
      borderRadius: "8px",
      fontSize: "14px",
      fontWeight: "500",
      cursor: "pointer",
      transition: "all 0.2s",
    },
    btnSecondary: {
      backgroundColor: "#f5f5f5",
      color: "#666",
    },
    btnPrimary: {
      backgroundColor: "#1976d2",
      color: "white",
    },
  };

  const formatNumber = (value: string) => {
    const number = value.replace(/[^\d]/g, "");
    return number.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const fetchListBalanceHistory = async () => {
    try {
      const data: BalanceLogRequestDto = {
        partnerId: "",
        type: filterStatus === "all" ? "" : filterStatus,
        refType: "",
        status: "",
        paging: {
          pageIndex: page,
          pageSize: rowsPerPage,
          search: debouncedSearchValue,
          name: "Created",
          sort: "desc",
          nameType: "Created",
          sortType: "desc",
        },
      };

      const response = await getBalanceHistory(data);
      if (response && response.status === 200) {
        setListBalanceHistory(response?.data?.data?.result);
        setTotalCount(response?.data?.data?.total || 0);
      }
    } catch (error) {}
  };
  useEffect(() => {
    fetchListBalanceHistory();
  }, [page, rowsPerPage, debouncedSearchValue, filterStatus]);
  return (
    <DashboardLayout>
      <Box sx={{ paddingTop: "20px", paddingLeft: 6, paddingRight: 6 }}>
        <Box style={styles.header}>
          <Typography variant="h2" style={styles.title}>
            Lịch sử giao dịch
          </Typography>
          <Typography style={styles.subtitle}>
            Quản lý và theo dõi tất cả các giao dịch của bạn
          </Typography>
        </Box>

        <Stack style={styles.balanceSection}>
          <Box style={styles.balanceCard}>
            <Box style={styles.balanceInfo}>
              <Box style={styles.balanceIcon}>💳</Box>
              <Box>
                <Typography style={styles.balanceText}>Số dư tài khoản</Typography>
                <Typography style={styles.balanceAmount}>
                  {Number(profile?.balance || 0).toLocaleString()}
                  {CURRENCY_UNIT}
                </Typography>
              </Box>
            </Box>
          </Box>
          <Button
            style={styles.topUpButton}
            onClick={() => {
              snackbar.info("Chức năng đang được phát triển");
              // setOpenTopUpDialog(true)
            }}
            onMouseEnter={(e) => {
              const target = e.target as HTMLButtonElement;
              target.style.transform = "translateY(-2px)";
              target.style.boxShadow = "0 6px 12px 2px rgba(244, 67, 54, .4)";
            }}
            onMouseLeave={(e) => {
              const target = e.target as HTMLButtonElement;
              target.style.transform = "translateY(0)";
              target.style.boxShadow = "0 3px 5px 2px rgba(244, 67, 54, .3)";
            }}
          >
            <Typography>➕</Typography>
            Nạp tiền
          </Button>
        </Stack>

        <Box sx={{ p: 3, bgcolor: "background.paper", borderRadius: 1, mb: 3 }}>
          <Box sx={{ display: "grid", gridTemplateColumns: { xs: "1fr", sm: "1fr 1fr" }, gap: 2 }}>
            <TextField
              fullWidth
              placeholder="Tìm kiếm giao dịch..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
            <FormControl fullWidth size="small">
              <Select
                labelId="status-filter-label"
                value={filterStatus}
                label="Trạng thái"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="all">Tất cả trạng thái</MenuItem>
                <MenuItem value="Deposit">Nạp tiền</MenuItem>
                <MenuItem value="Deduction">Khấu trừ</MenuItem>
                <MenuItem value="Refund">Hoàn tiền</MenuItem>
                <MenuItem value="Adjustment">Điều chỉnh số dư</MenuItem>
                <MenuItem value="Chargeback">Bồi hoàn do tranh chấp</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>

        <Card style={{ ...styles.card, padding: 0 }}>
          <Table style={styles.table as React.CSSProperties}>
            <TableHead>
              <TableRow>
                <TableCell style={styles.tableHeader as React.CSSProperties}>STT</TableCell>
                <TableCell style={styles.tableHeader as React.CSSProperties}>
                  Mã giao dịch
                </TableCell>
                <TableCell style={styles.tableHeader as React.CSSProperties} sx={{ width: "10%" }}>
                  Loại
                </TableCell>
                <TableCell style={styles.tableHeader as React.CSSProperties}>Mô tả</TableCell>
                <TableCell
                  style={{ ...styles.tableHeader, textAlign: "left" }}
                  sx={{ minWidth: "150px" }}
                >
                  Số tiền
                </TableCell>
                <TableCell style={styles.tableHeader as React.CSSProperties}>Thời gian</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.isArray(listBalanceHistory) &&
                listBalanceHistory.length > 0 &&
                listBalanceHistory.map((transaction, index) => (
                  <TableRow
                    key={transaction.id}
                    onMouseEnter={(e) => {
                      const target = e.target as HTMLElement;
                      if (target.parentElement) {
                        target.parentElement.style.backgroundColor = "#f8f9fa";
                      }
                    }}
                    onMouseLeave={(e) => {
                      const target = e.target as HTMLElement;
                      if (target.parentElement) {
                        target.parentElement.style.backgroundColor = "white";
                      }
                    }}
                  >
                    <TableCell style={styles.tableCell}>{page * rowsPerPage + index + 1}</TableCell>
                    <TableCell style={styles.tableCell}>
                      <span>{transaction.balanceLogNo}</span>
                    </TableCell>
                    <TableCell style={styles.tableCell}>
                      {getBalanceHistoryVietnamese(transaction.type)}
                    </TableCell>
                    <TableCell style={styles.tableCell}>{transaction.message}</TableCell>
                    <TableCell style={{ ...styles.tableCell, textAlign: "left" }}>
                      <Typography
                      // style={
                      //   transaction.type !== "Deduction"
                      //     ? styles.amountPositive
                      //     : styles.amountNegative
                      // }
                      >
                        {/* {transaction.type !== "Deduction" ? "+" : "-"} */}
                        {formatMoney(transaction.amount)}
                        {CURRENCY_UNIT}
                      </Typography>
                    </TableCell>

                    <TableCell style={styles.tableCell}>
                      {dayjs(transaction.createdDate).format("DD/MM/YYYY: HH:mm:ss")}
                    </TableCell>
                  </TableRow>
                ))}

              {listBalanceHistory.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} style={{ textAlign: "center", padding: "20px" }}>
                    <Typography variant="body1" color="textSecondary">
                      Không có dữ liệu
                    </Typography>
                  </TableCell>{" "}
                </TableRow>
              )}
            </TableBody>
          </Table>

          <TablePagination
            labelRowsPerPage="Số hàng mỗi trang"
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={rowPerPageOptionsDefault}
          />
        </Card>
      </Box>

      {openTopUpDialog && (
        <Dialog
          open={openTopUpDialog}
          onClose={() => setOpenTopUpDialog(false)}
          PaperProps={{
            sx: {
              borderRadius: 2,
              p: 1,
              maxHeight: "90vh",
              width: "400px",
            },
          }}
        >
          <div onClick={(e) => e.stopPropagation()}>
            <Box style={styles.modalHeader}>
              <Typography style={styles.modalTitle}>💳 Nạp tiền vào tài khoản</Typography>
            </Box>
            <Box style={styles.modalBody}>
              <Box style={styles.formGroup}>
                <Typography style={styles.label}>Số tiền nạp</Typography>
                <TextField
                  fullWidth
                  type="text"
                  size="small"
                  placeholder="Nhập số tiền cần nạp"
                  value={topUpAmount}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (/^\d*$/.test(value.replace(/,/g, ""))) {
                      const formatted = formatNumber(value);
                      setTopUpAmount(formatted);
                    }
                  }}
                  onKeyDown={(e) => {
                    if (["e", "E", "-", "+", "."].includes(e.key)) {
                      e.preventDefault();
                    }
                  }}
                  InputProps={{
                    inputProps: {
                      min: 0,
                      style: { textAlign: "left" },
                    },
                    endAdornment: <InputAdornment position="end">đ</InputAdornment>,
                  }}
                  sx={{
                    "& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button": {
                      "-webkit-appearance": "none",
                      margin: 0,
                    },
                    "& input[type=number]": {
                      "-moz-appearance": "textfield",
                    },
                    height: "45px",
                  }}
                />
              </Box>
              <Box>
                <Typography style={styles.label}>Phương thức thanh toán</Typography>
                <Select
                  value={paymentMethod}
                  sx={{ width: "100%", marginTop: "8px", height: "45px" }}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                >
                  <MenuItem value="banking">Chuyển khoản ngân hàng</MenuItem>
                  <MenuItem value="momo">Ví MoMo</MenuItem>
                  <MenuItem value="zalopay">ZaloPay</MenuItem>
                  <MenuItem value="card">Thẻ tín dụng/ghi nợ</MenuItem>
                </Select>
              </Box>
            </Box>
            <Box style={styles.modalActions}>
              <Button variant="outlined" onClick={() => setOpenTopUpDialog(false)}>
                Hủy
              </Button>
              <Button variant="contained" onClick={handleTopUp} disabled={!topUpAmount}>
                Nạp tiền
              </Button>
            </Box>
          </div>
        </Dialog>
      )}
    </DashboardLayout>
  );
};

export default BalanceHistory;
