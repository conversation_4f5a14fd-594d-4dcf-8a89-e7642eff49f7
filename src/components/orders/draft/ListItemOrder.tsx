import React, { useState } from "react";
import {
  Drawer,
  List,
  ListItem,
  ListItemText,
  Button,
  Typography,
  Divider,
  IconButton,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Box,
  Tooltip,
  Dialog,
  DialogContent,
  Stack,
} from "@mui/material";
import {
  ShoppingCart as ShoppingCartIcon,
  Close as CloseIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import { formatMoney } from "@/src/utils/format-money";
import { formatPrice } from "@/src/api/types/membership.types";
import TruncatedText from "../../truncated-text/truncated-text";
import { useAppSelector } from "@/src/redux/hooks";
import { useValidImage } from "@/src/hooks/useValidImage";

const ItemOrder = ({
  item,
  index,
  handleOpenZoom,
  currentShop,
  updateQuantity,
  removeItem,
  displayVariantInfo,
  taxRateCheck,
  taxPrice,
}) => {
  const imgItemSrc = useValidImage(item.images?.[0]?.link, currentShop?.shopLogo?.link);
  const imgVariantSrc = useValidImage(item.variantImage?.link, imgItemSrc);
  return (
    <TableRow key={item.itemsId}>
      <TableCell>{index + 1}</TableCell>
      <TableCell sx={{ width: 150 }}>
        <Box display="flex" gap={1} alignItems="start">
          <Box
            sx={{
              width: 50,
              height: 50,
              overflow: "hidden",
              borderRadius: 1,
              boxShadow: 3,
              flexShrink: 0,
              cursor: "pointer",
              "&:hover": {
                opacity: 0.8,
              },
            }}
            onClick={() => handleOpenZoom(imgVariantSrc)}
          >
            <img
              src={imgVariantSrc}
              alt={item.itemsName}
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />
          </Box>
          <Box sx={{ ml: 0.5 }}>
            <TruncatedText typographyProps={{ fontWeight: 500 }} text={item?.itemsName} />
            <Typography
              sx={{
                mt: 0.4,
                borderRadius: 1,
              }}
            >
              {displayVariantInfo(item)}
            </Typography>
          </Box>
        </Box>
      </TableCell>

      <TableCell align="center">
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: 1,
            "& .MuiIconButton-root": {
              bgcolor: "grey.100",
              "&:hover": {
                bgcolor: "grey.200",
              },
              "&.Mui-disabled": {
                bgcolor: "grey.50",
              },
            },
          }}
        >
          <IconButton
            onClick={() => updateQuantity(item.itemsId, item.quantity - 1)}
            disabled={item.quantity <= 1}
            size="small"
          >
            <RemoveIcon fontSize="small" />
          </IconButton>
          <TextField
            size="small"
            value={item.quantity}
            onChange={(e) => {
              const value = e.target.value;
              if (/^\d*$/.test(value) && parseInt(value) > 0) {
                updateQuantity(item.itemsId, parseInt(value));
              }
            }}
            onKeyDown={(e) => {
              if (["e", "E", "-", "+", "."].includes(e.key)) {
                e.preventDefault();
              }
            }}
            inputProps={{
              min: 1,
              style: {
                textAlign: "center",
                padding: "4px 8px",
              },
            }}
            sx={{
              width: 60,
              "& .MuiOutlinedInput-root": {
                "& fieldset": {
                  borderColor: "grey.300",
                },
                "&:hover fieldset": {
                  borderColor: "grey.400",
                },
              },
            }}
          />
          <IconButton onClick={() => updateQuantity(item.itemsId, item.quantity + 1)} size="small">
            <AddIcon fontSize="small" />
          </IconButton>
        </Box>
      </TableCell>
      <TableCell>{formatMoney(item.price)}đ</TableCell>
      <TableCell>{formatMoney(item.price * item.quantity)}đ</TableCell>
      <TableCell>{taxRateCheck}%</TableCell>
      <TableCell>{formatMoney(item.price * item.quantity * (taxRateCheck / 100))}đ</TableCell>
      <TableCell>{formatMoney(item.price * item.quantity + taxPrice)}đ</TableCell>

      <TableCell
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "sticky",
          right: 0,
          backgroundColor: "#fff",
          zIndex: 2,
          padding: "45px 16px",
          boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
        }}
      >
        <IconButton onClick={() => removeItem(item.itemsId)}>
          <DeleteIcon sx={{ color: "red" }} />
        </IconButton>
      </TableCell>
    </TableRow>
  );
};

export default function ListItemOrder({ cartItems, updateQuantity, removeItem, taxRateShop }) {
  const [openZoom, setOpenZoom] = useState(false);
  const [selectedImage, setSelectedImage] = useState("");
  const currentShop = useAppSelector((state) => state.shop.currentShop);

  const handleOpenZoom = (imageUrl) => {
    setSelectedImage(imageUrl);
    setOpenZoom(true);
  };

  const handleCloseZoom = () => {
    setOpenZoom(false);
    setSelectedImage("");
  };
  const displayVariantInfo = (item) => {
    const variantInfo = [];

    if (item.variantNameOne && item.variantValueOne) {
      variantInfo.push(
        <Box key="variantOne" display="flex" sx={{ fontSize: 14 }}>
          <Stack marginRight={0.5} flexDirection={"row"} alignItems={"center"}>
            <TruncatedText typographyProps={{ maxWidth: 100 }} text={`${item.variantNameOne}`} />
            <Typography sx={{ mr: 0.5 }}>:</Typography>
            <TruncatedText typographyProps={{ maxWidth: 100 }} text={item.variantValueOne} />
          </Stack>
        </Box>
      );
    }

    if (item.variantNameTwo && item.variantValueTwo) {
      variantInfo.push(
        <Box key="variantTwo" display="flex" sx={{ fontSize: 14 }}>
          <Stack marginRight={0.5} flexDirection={"row"} alignItems={"center"}>
            <TruncatedText typographyProps={{ maxWidth: 100 }} text={`${item.variantNameTwo}`} />
            <Typography sx={{ mr: 0.5 }}>:</Typography>
            <TruncatedText typographyProps={{ maxWidth: 100 }} text={item.variantValueTwo} />
          </Stack>
        </Box>
      );
    }

    if (item.variantNameThree && item.variantValueThree) {
      variantInfo.push(
        <Box key="variantThree" display="flex" sx={{ fontSize: 14 }}>
          <Stack marginRight={0.5} flexDirection={"row"} alignItems={"center"}>
            <TruncatedText typographyProps={{ maxWidth: 100 }} text={`${item.variantNameThree}`} />
            <Typography sx={{ mr: 0.5 }}>:</Typography>
            <TruncatedText typographyProps={{ maxWidth: 100 }} text={item.variantValueThree} />
          </Stack>
        </Box>
      );
    }

    return variantInfo;
  };
  return (
    <>
      <Box marginTop={2}>
        <Divider />

        {/* Bảng giỏ hàng */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ minWidth: 5 }}>STT</TableCell>
                <TableCell sx={{ minWidth: 250 }}>Tên sản phẩm</TableCell>
                <TableCell align="center" sx={{ minWidth: 100 }}>
                  Số lượng
                </TableCell>
                <TableCell sx={{ minWidth: 100 }}>Đơn giá</TableCell>
                <TableCell sx={{ minWidth: 200 }}>Thành tiền chưa thuế</TableCell>
                <TableCell sx={{ minWidth: 150 }}>Thuế suất GTGT</TableCell>
                <TableCell sx={{ minWidth: 200 }}>Tiền thuế GTGT</TableCell>
                <TableCell sx={{ minWidth: 200 }}>Thành tiền đã có thuế GTGT</TableCell>
                <TableCell
                  sx={{
                    minWidth: 100,
                    position: "sticky",
                    right: 0,
                    bottom: 0,
                    backgroundColor: "#fff",
                    zIndex: 3,
                    boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                    padding: { xs: "16px 4px", sm: "30px 16px" },
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    width: { xs: "70px", sm: "90px" },
                  }}
                >
                  Quản lý
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {cartItems.length > 0 ? (
                cartItems.map((item, index) => {
                  const taxRateCheck =
                    item.customTaxRate === 0 || item.customTaxRate
                      ? item.customTaxRate
                      : item.taxRate === 0 || item.taxRate
                      ? item.taxRate
                      : taxRateShop;
                  const taxPrice = item.price * item.quantity * (taxRateCheck / 100);
                  return (
                    <ItemOrder
                      currentShop={currentShop}
                      key={item.itemsId}
                      index={index}
                      item={item}
                      updateQuantity={updateQuantity}
                      removeItem={removeItem}
                      taxRateCheck={taxRateCheck}
                      taxPrice={taxPrice}
                      displayVariantInfo={displayVariantInfo}
                      handleOpenZoom={handleOpenZoom}
                    />
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={9} align="center">
                    Không có sản phẩm
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* <Divider />
        <Typography variant="h6" gutterBottom>
          Tổng cộng: ${calculateTotal()}
        </Typography>
        <Button fullWidth variant="contained" color="primary" onClick={() => alert('Thanh toán')}>
          Thanh toán
        </Button> */}
      </Box>
      <Dialog
        open={openZoom}
        onClose={handleCloseZoom}
        maxWidth={false}
        PaperProps={{
          sx: {
            backgroundColor: "background.paper",
            borderRadius: 1,
            p: 1,
            maxWidth: "90vw",
            maxHeight: "90vh",
          },
        }}
      >
        <DialogContent sx={{ p: 0 }}>
          <img
            src={selectedImage}
            alt="Zoomed product"
            style={{
              maxWidth: "100%",
              maxHeight: "80vh",
              objectFit: "contain",
            }}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
