import { TableProps as AntdTableProps, TableColumnType } from 'antd';
import { ReactNode } from 'react';

export interface BaseTableColumn<T = any> extends Omit<TableColumnType<T>, 'render'> {
  key: string;
  title: string;
  dataIndex?: string;
  width?: number | string;
  fixed?: 'left' | 'right';
  align?: 'left' | 'center' | 'right';
  sorter?: boolean | ((a: T, b: T) => number);
  render?: (value: any, record: T, index: number) => ReactNode;
  ellipsis?: boolean;
  responsive?: ('xs' | 'sm' | 'md' | 'lg' | 'xl' | 'xxl')[];
}

export interface TableAction<T = any> {
  key: string;
  label: string;
  icon?: ReactNode;
  onClick: (record: T) => void;
  disabled?: (record: T) => boolean;
  visible?: (record: T) => boolean;
  type?: 'primary' | 'default' | 'danger';
  tooltip?: string;
}

export interface CommonTableProps<T = any> extends Omit<AntdTableProps<T>, 'columns'> {
  columns: BaseTableColumn<T>[];
  data: T[];
  loading?: boolean;
  
  // Pagination props
  pagination?: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
    showTotal?: (total: number, range: [number, number]) => string;
    pageSizeOptions?: string[];
    onChange?: (page: number, pageSize: number) => void;
  } | false;
  
  // Selection props
  rowSelection?: {
    selectedRowKeys?: React.Key[];
    onChange?: (selectedRowKeys: React.Key[], selectedRows: T[]) => void;
    onSelect?: (record: T, selected: boolean, selectedRows: T[], nativeEvent: Event) => void;
    onSelectAll?: (selected: boolean, selectedRows: T[], changeRows: T[]) => void;
    getCheckboxProps?: (record: T) => { disabled?: boolean; name?: string };
    type?: 'checkbox' | 'radio';
    fixed?: boolean;
    columnWidth?: string | number;
    columnTitle?: string | ReactNode;
    hideSelectAll?: boolean;
  };
  
  // Actions column
  actions?: TableAction<T>[];
  actionsColumnProps?: {
    title?: string;
    width?: number | string;
    fixed?: 'left' | 'right';
  };
  
  // Search and filter
  searchable?: boolean;
  searchPlaceholder?: string;
  onSearch?: (value: string) => void;
  
  // Toolbar
  toolbar?: {
    title?: string;
    extra?: ReactNode;
    showRefresh?: boolean;
    onRefresh?: () => void;
  };
  
  // Empty state
  emptyText?: string;
  emptyDescription?: string;
  emptyAction?: {
    text: string;
    onClick: () => void;
  };
  
  // Table settings
  bordered?: boolean;
  size?: 'small' | 'middle' | 'large';
  sticky?: boolean;
  scroll?: { x?: number | string; y?: number | string };
  
  // Row props
  rowKey?: string | ((record: T) => string);
  expandable?: AntdTableProps<T>['expandable'];
  
  // Custom styling
  className?: string;
  tableClassName?: string;
  
  // Callbacks
  onRow?: (record: T, index?: number) => React.HTMLAttributes<any>;
  onChange?: (pagination: any, filters: any, sorter: any, extra: any) => void;
}

export interface TableRef {
  refresh: () => void;
  getSelectedRows: () => any[];
  clearSelection: () => void;
}
