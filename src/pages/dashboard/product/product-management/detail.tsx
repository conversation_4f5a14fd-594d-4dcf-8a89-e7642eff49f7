import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { Box, Grid, Button, CircularProgress, IconButton } from "@mui/material";
import { useFormik } from "formik";
import DashboardLayout from "@/src/layouts/dashboard";
import { tokens } from "@/src/locales/tokens";
import { useProduct } from "@/src/api/hooks/dashboard/product/use-product";
import { useService } from "@/src/api/hooks/dashboard/product/use-service";
import { useStoreId } from "@/src/hooks/use-store-id";
import { StorageService } from "nextjs-api-lib";
import { ProductFormValues, initialProductValues } from "@/src/types/product/form";
import { useWarehouse } from "@/src/api/hooks/dashboard/product/use-warehouse";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import { useSnackbar } from "notistack";
import { ExistingMediaFile, scrollToTop, validationSchema } from "./create/create";

// Components
import TypeSelection from "./create/components/type-selection";
import MediaUpload from "./create/components/media-upload";
import Price from "./create/components/price";
import PurchaseQuantity from "./create/components/purchase-quantity";
import VariantSettings from "./create/components/variant-settings";
import ShippingInformation from "./create/components/shipping-information";
import SeoSettings from "./create/components/seo-settings";
import { paths } from "@/src/paths";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { ProductItemOptionGroup } from "@/src/components/product/ProductItemOptionGroup";
import { useItemOption } from "@/src/api/hooks/item-option/use-item-option";
import BasicInformation from "./create/components/basic-information";
import DisplaySettings from "./create/components/display-settings";
import CategorySelection from "./create/components/category-selection";
import ProductTaxRate from "./create/components/product-tax-rate";
import { useShop } from "@/src/api/hooks/shop/use-shop";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { Padding } from "@mui/icons-material";
import { CommonMediaUpload } from "@/src/components/common-media-upload";
import { FILE_SIZE_2MB, MAX_FILE_IMAGE } from "@/src/constants/constant";
import { ImageProcessor } from "@/src/utils/image-processor";
import { CreateFileGroupRequest, GetGroupFileRequest, RefType } from "@/src/api/types/media.types";
import { useMedia } from "@/src/api/hooks/media/use-media";
import { FileType } from "@/src/constants/file-types";
import { GetProductCategoryRequest } from "@/src/api/types/product-category.types";
import DOMPurify from "dompurify";

type ItemType = "Product" | "Service";

// Define common snackbar configuration
const SNACKBAR_CONFIG = {
  autoHideDuration: 3000,
  anchorOrigin: {
    vertical: "top" as const,
    horizontal: "right" as const,
  },
};

// Define validation error message function
const showValidationError = (message: string, enqueueSnackbar: any) => {
  enqueueSnackbar(message, {
    variant: "error",
    ...SNACKBAR_CONFIG,
  });
};

const UpdateProduct = () => {
  const { t } = useTranslation();
  const { detailShop } = useShop();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [warehouses, setWarehouses] = useState<any[]>([]);
  const [extraItemOptionGroups, setExtraItemOptionGroups] = useState([]);
  const { getWarehouse } = useWarehouse();
  const { getProductDetail, updateProduct } = useProduct();
  const { getServiceDetail, updateService } = useService();
  const { itemOptionUser } = useItemOption();
  const storeId = useStoreId();
  const partnerId = StorageService.get("partnerId") || "";
  const [categories, setCategories] = useState<any[]>([]);
  const { getProductCategoryTree } = useProductCategory();
  const [isLoading, setIsLoading] = useState(true);
  // Changed: Get id from query parameter instead of dynamic route
  const { id, type } = router.query;
  const { enqueueSnackbar } = useSnackbar();
  const [shopTaxRate, setShopTaxRate] = useState<number>(0);
  const [existingFiles, setExistingFiles] = useState<ExistingMediaFile[]>([]);
  const [localFiles, setLocalFiles] = useState<File[]>([]);
  const [defaultGroupId, setDefaultGroupId] = useState<string>("");
  const { getGroups, uploadFile } = useMedia();
  const [errorMsg, setErrorMsg] = useState<string>("");

  useEffect(() => {
    const fetchDefaultGroup = async () => {
      try {
        const data: GetGroupFileRequest = {
          ShopId: storeId,
          Skip: 0,
          Limit: 10,
        };
        const response = await getGroups(data);
        if (response.data?.data?.length > 0) {
          setDefaultGroupId(response.data.data[0].groupFileId);
        }
      } catch (error) {}
    };
    fetchDefaultGroup();
  }, [storeId]);

  // Initialize itemsType from query parameter
  const [itemsType, setItemsType] = useState<ItemType>(() => {
    return (type as ItemType) || "Product";
  });

  useEffect(() => {
    const fetchDetailShop = async () => {
      const res = await detailShop(storeId);
      if (res && res?.status === 200) {
        setShopTaxRate(res?.data?.defaultTaxRate);
      }
    };
    fetchDetailShop();
  }, [storeId]);

  const MAX_TOTAL_IMAGES = 20;

  const countVariantImages = (variants: any[]) =>
    variants.filter((v) => v?.variantImage?.link).length;

  const handleSubmitClick = async () => {
    try {
      const variantImageCount = countVariantImages(formik.values.listVariant || []);
      const totalImages = localFiles.length + existingFiles.length + variantImageCount;

      if (totalImages > MAX_TOTAL_IMAGES) {
        enqueueSnackbar(`Không thể chọn quá ${MAX_FILE_IMAGE} ảnh cho sản phẩm và các biến thể!`, {
          variant: "error",
        });
        return;
      }
      const errors = await formik.validateForm();
      if (Object.keys(errors).length > 0) {
        const firstError = Object.values(errors)[0];
        if (firstError) {
          showValidationError(String(firstError), enqueueSnackbar);
          scrollToTop();
          return;
        }
        return;
      }

      Object.keys(formik.values).forEach((key) => {
        formik.setFieldTouched(key, true, false);
      });

      if (localFiles.length === 0 && existingFiles.length === 0) {
        showValidationError("Bạn cần chọn ít nhất 1 ảnh!", enqueueSnackbar);
        scrollToTop();
        return;
      }
      let uploadedFiles = [];
      if (localFiles.length > 0) {
        for (const file of localFiles) {
          const processedFile = await ImageProcessor.processImage(file);
          const data: CreateFileGroupRequest = {
            FileUpload: processedFile,
            ShopId: storeId,
            GroupFileId: defaultGroupId,
            RefType: itemsType === "Product" ? RefType.Product : RefType.Service,
            RefId: "",
          };
          const response = await uploadFile(data);
          uploadedFiles.push(response.data);
        }
      }

      const newImages = uploadedFiles.map((file) => ({
        type: file.type === "IMAGE" ? FileType.IMAGE : FileType.VIDEO,
        link: file.url || file.link,
        mediaFileId: file.mediaFileId,
      }));
      const updatedImages = [...(formik.values.images || []), ...newImages];

      if (updatedImages.length > 0) {
        formik.values.images = [...(existingFiles || []), ...newImages];
        formik.setFieldValue("images", updatedImages, true);

        // Validate field
        await formik.validateField("images");

        // Kiểm tra với updatedImages thay vì formik.values.images
        if (updatedImages.length === 0) {
          showValidationError("Bạn cần chọn ít nhất 1 ảnh!", enqueueSnackbar);
          scrollToTop();
          return;
        }

        // Xóa localFiles sau khi upload thành công
        setLocalFiles([]);

        const requestData = {
          ...formik.values,
          partnerId,
          shopId: storeId,
          warehouseId: formik.values.warehouseId || warehouses[0]?.warehouseId,
          itemsType,
          description: DOMPurify.sanitize(formik.values.description),
          priceCapital: formik.values.isVariant ? 0 : formik.values.priceCapital,
          price: formik.values.isVariant ? 0 : formik.values.price,
          priceReal: formik.values.isVariant ? 0 : formik.values.priceReal,
          quantity: formik.values.isVariant ? 0 : formik.values.quantity,
          quantityPurchase: formik.values.isVariant ? 0 : formik.values.quantityPurchase,
          listVariant: formik.values.isVariant ? formik.values.listVariant : [],
          itemsInfo: formik.values?.description,
          images: formik.values.images,
          itemsPosition: Number(formik.values.itemsPosition),
        };

        if (itemsType === "Product") {
          await updateProduct({ ...requestData, itemsId: id });
          enqueueSnackbar("Cập nhật sản phẩm thành công", { variant: "success" });
        } else {
          await updateService({ ...requestData });
          enqueueSnackbar("Cập nhật dịch vụ thành công", { variant: "success" });
        }

        // Gọi submit formik
        formik.handleSubmit();
      }

      formik.handleSubmit();
    } catch (error) {
      scrollToTop();
    }
  };

  // Handle form submission
  const handleSubmit = async (values: ProductFormValues) => {
    // Validate form before submit
    // values.images = [...formik.values.images];
    const errors = await formik.validateForm();
    if (Object.keys(errors).length > 0) {
      const firstError = Object.values(errors)[0];
      if (firstError) {
        showValidationError(String(firstError), enqueueSnackbar);
        scrollToTop();
      }
      return;
    }

    if (!validateForm(values, enqueueSnackbar)) {
      return;
    }

    setIsSubmitting(true);
    try {
      router.push(paths.dashboard.product.productManagement);
    } catch (error) {
      showValidationError("Có lỗi xảy ra", enqueueSnackbar);
      scrollToTop();
    } finally {
      setIsSubmitting(false);
    }
  };

  // Initialize formik with validation schema
  const formik = useFormik<ProductFormValues>({
    initialValues: {
      ...initialProductValues,
      shopId: storeId,
      partnerId: partnerId as string,
      itemsType,
    },
    validationSchema,
    validateOnChange: true,
    validateOnBlur: true,
    onSubmit: handleSubmit,
  });

  // Fetch detail data
  useEffect(() => {
    const fetchItemDetail = async () => {
      if (id && storeId) {
        setIsLoading(true);
        try {
          const response = await (itemsType === "Product"
            ? getProductDetail(storeId, id as string)
            : getServiceDetail(storeId, id as string));

          const detail = response.data as any;

          setExistingFiles(detail.images);
          // Update form values
          formik.setValues({
            // Basic info
            itemsId: detail.itemsId || "",
            itemsCode: detail.itemsCode || "",
            itemsType: detail.itemsType || "Product",
            itemsName: detail.itemsName || "",
            itemsInfo: detail.itemsInfo || "",
            description: detail.itemsInfo || "",
            sold: detail.sold,

            // Categories
            categoryIds: detail.categoryIds || null,

            // Display settings
            isVisible: detail.isVisible ?? true,
            isTop: detail.isTop ?? false,
            typePublish: detail.typePublish || "Publish",
            status: detail.status || "Actived",
            itemsPosition: detail.itemsPosition || 0,

            // Media
            images: detail.images || [],

            // Variants
            isVariant: Boolean(detail.isVariant),
            listVariant: detail.listVariant || [],

            // Pricing
            priceCapital: detail.priceCapital || 0,
            price: detail.price || 0,
            priceReal: detail.priceReal || 0,

            // Quantity
            quantity: detail.quantity || 0,
            quantityPurchase: detail.quantityPurchase || 0,
            quantityPurchaseMin: detail.quantityPurchaseMin || 1,
            quantityPurchaseMax: detail.quantityPurchaseMax || 0,

            // Shipping Information
            warehouseId: detail.warehouseId || "",
            itemsWeight: detail.itemsWeight || 0,
            itemsLength: detail.itemsLength || 0,
            itemsWidth: detail.itemsWidth || 0,
            itemsHeight: detail.itemsHeight || 0,

            // SEO
            seoTags: detail.seoTags || [
              {
                pageTitle: "",
                pageDesc: "",
                tags: "",
                url: "",
              },
            ],
            extraItemOptionGroups: detail.extraItemOptionGroups || [],
            transportType: detail.transportType || [],

            // System fields
            shopId: storeId,
            partnerId: partnerId as string,
            created: detail.created,
            updated: detail.updated,
            customTaxRate: detail.customTaxRate,
          });

          if (detail.extraItemOptionGroups.length > 0) {
            const listItemOptionGroupId = detail.extraItemOptionGroups.map(
              (i) => i.itemOptionGroupId
            );
            const listItemOptionUserResponse = await itemOptionUser(listItemOptionGroupId);
            if (listItemOptionUserResponse?.data) {
              const listItemOptionUser = listItemOptionUserResponse.data?.data;
              setExtraItemOptionGroups(listItemOptionUser);
            }
          }

          // Update itemsType if different from current
          if (detail.itemsType !== itemsType) {
            setItemsType(detail.itemsType as ItemType);
          }
        } catch (error) {
          router.push(paths.dashboard.product.productManagement);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchItemDetail();
  }, [storeId, id]);

  // Fetch initial data (categories and warehouses)
  useEffect(() => {
    const fetchInitialData = async () => {
      if (!storeId) return;

      setIsLoading(true);
      try {
        const params: GetProductCategoryRequest = {
          categoryType: itemsType,
          shopId: storeId,
          partnerId: StorageService.get("partnerId") as string | null,
          paging: {
            name: "Created",
            nameType: "Created",
            sortType: "desc",
            sort: "desc",
            pageIndex: 0,
            pageSize: 100,
          },
        };
        const [categoryResponse, warehouseResponse] = await Promise.all([
          getProductCategoryTree(params),
          getWarehouse(0, 100),
        ]);

        const categoryList = categoryResponse.data.data.result || [];
        const warehouseList = warehouseResponse.data.data || [];

        setCategories(categoryList);
        setWarehouses(warehouseList);
      } catch (error) {
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialData();
  }, [storeId, itemsType]);

  // Handle form validation
  const validateForm = (values: ProductFormValues, enqueueSnackbar: any) => {
    if (values.isVariant && (!values.listVariant || values.listVariant.length === 0)) {
      showValidationError("Vui lòng thêm ít nhất một phiên bản", enqueueSnackbar);
      return false;
    }

    if (values.isVariant && values.listVariant?.length === 0) {
      formik.setFieldValue("isVariant", false);
    }

    return true;
  };

  // Loading spinner
  if (isLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
          position: "relative",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  const handleCancel = () => {
    router.back();
  };

  const handleFilesChange = (files: File[]) => {
    setLocalFiles(files);
  };

  const handleRemove = (index: number, isExisting: boolean) => {
    if (isExisting) {
      const updated = [...existingFiles];
      updated.splice(index, 1);
      setExistingFiles(updated);
    } else {
      const localIdx = index - existingFiles.length;
      setLocalFiles((prev) => prev.filter((_, i) => i !== localIdx));
      // Không cần setLocalPreviews ở đây, useEffect sẽ tự cập nhật
    }
    // Local file sẽ tự động cập nhật qua handleFilesChange
  };

  return (
    <DashboardLayout>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
          position: "relative",
        }}
      >
        <Box sx={{ p: { xs: 2, md: 3 }, overflow: "auto" }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 3,
              paddingBottom: "20px",
              marginBottom: "20px",
              borderBottom: "1px solid #bdbdbd",
            }}
          >
            <IconButton onClick={handleCancel} sx={{ mr: 2 }}>
              <ArrowBackIcon />
            </IconButton>
            <TitleTypography sx={{ fontSize: "20px !important" }}>
              {itemsType === "Product"
                ? "Hàng hoá/Sản phẩm/Cập nhật sản phẩm"
                : "Hàng hoá/Dịch vụ/Cập nhật dịch vụ"}
            </TitleTypography>
          </Box>

          <Box>
            {/* <Box sx={{ mb: 3 }}>
              <TypeSelection itemsType={itemsType} isEdit={true} setItemsType={setItemsType} />
            </Box> */}

            {/* Main Content */}
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 3,
                  }}
                >
                  <BasicInformation formik={formik} itemsType={itemsType} />
                  {/* <MediaUpload formik={formik} /> */}
                  <CommonMediaUpload
                    caption="Thêm ảnh"
                    maxFiles={20}
                    accept={{
                      "image/*": [".png", ".jpg", ".jpeg"],
                    }}
                    maxSize={FILE_SIZE_2MB} // 2MB
                    existingFiles={existingFiles}
                    setExistingFiles={setExistingFiles}
                    localFiles={localFiles}
                    setLocalFiles={setLocalFiles}
                    onFilesChange={handleFilesChange}
                    onRemove={handleRemove}
                    defaultGroupId={defaultGroupId}
                    errorMsg={errorMsg}
                    setErrorMsg={setErrorMsg}
                  />
                  {itemsType === "Product" && <VariantSettings formik={formik} />}
                  {itemsType === "Product" && (
                    <ProductItemOptionGroup
                      formik={formik}
                      listDefaultItemOptionGroups={extraItemOptionGroups}
                    />
                  )}

                  {/* Show price and quantity only when not a variant */}
                  {!formik.values.isVariant && (
                    <>
                      <Price formik={formik} />
                      <PurchaseQuantity formik={formik} />
                    </>
                  )}
                  {itemsType === "Product" && (
                    <ShippingInformation formik={formik} warehouses={warehouses} />
                  )}

                  <SeoSettings formik={formik} />
                </Box>
              </Grid>

              {/* Right Sidebar */}
              <Grid item xs={12} md={4}>
                <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
                  <DisplaySettings formik={formik} />
                  <CategorySelection
                    formik={formik}
                    categories={categories}
                    itemsType={itemsType}
                  />
                  <ProductTaxRate formik={formik} shopTaxRate={shopTaxRate} />
                </Box>
              </Grid>
            </Grid>

            {/* Footer Actions */}
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                position: "relative",
                paddingBottom: "80px",
              }}
            >
              <Box
                sx={{
                  position: "fixed",
                  bottom: 0,
                  left: 0,
                  right: 0,
                  padding: "10px 32px",
                  borderTop: 1,
                  borderColor: "#e5e6e7",
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 2,
                  backgroundColor: "background.paper",
                  zIndex: 1000,
                }}
              >
                <Button variant="outlined" onClick={() => router.back()}>
                  {t(tokens.common.cancel)}
                </Button>
                <Button
                  variant="contained"
                  onClick={() => handleSubmitClick()}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? <CircularProgress size={24} /> : t(tokens.common.save)}
                </Button>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </DashboardLayout>
  );
};

export default UpdateProduct;
