import { useState, useCallback, useMemo } from 'react';

interface UseTableSelectionProps<T> {
  rowKey?: string | ((record: T) => string);
  onSelectionChange?: (selectedKeys: React.Key[], selectedRows: T[]) => void;
  getCheckboxProps?: (record: T) => { disabled?: boolean; name?: string };
}

interface UseTableSelectionReturn<T> {
  selectedRowKeys: React.Key[];
  selectedRows: T[];
  rowSelection: {
    selectedRowKeys: React.Key[];
    onChange: (selectedRowKeys: React.Key[], selectedRows: T[]) => void;
    onSelect: (record: T, selected: boolean, selectedRows: T[], nativeEvent: Event) => void;
    onSelectAll: (selected: boolean, selectedRows: T[], changeRows: T[]) => void;
    getCheckboxProps?: (record: T) => { disabled?: boolean; name?: string };
  };
  clearSelection: () => void;
  selectAll: (data: T[]) => void;
  isSelected: (record: T) => boolean;
  getSelectedCount: () => number;
}

export const useTableSelection = <T = any>({
  rowKey = 'id',
  onSelectionChange,
  getCheckboxProps,
}: UseTableSelectionProps<T> = {}): UseTableSelectionReturn<T> => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<T[]>([]);

  const getRowKey = useCallback((record: T): React.Key => {
    return typeof rowKey === 'function' ? rowKey(record) : record[rowKey as string];
  }, [rowKey]);

  const handleSelectionChange = useCallback((keys: React.Key[], rows: T[]) => {
    setSelectedRowKeys(keys);
    setSelectedRows(rows);
    onSelectionChange?.(keys, rows);
  }, [onSelectionChange]);

  const handleSelect = useCallback((record: T, selected: boolean, rows: T[], nativeEvent: Event) => {
    const key = getRowKey(record);
    let newKeys: React.Key[];
    let newRows: T[];

    if (selected) {
      newKeys = [...selectedRowKeys, key];
      newRows = [...selectedRows, record];
    } else {
      newKeys = selectedRowKeys.filter(k => k !== key);
      newRows = selectedRows.filter(row => getRowKey(row) !== key);
    }

    handleSelectionChange(newKeys, newRows);
  }, [selectedRowKeys, selectedRows, getRowKey, handleSelectionChange]);

  const handleSelectAll = useCallback((selected: boolean, rows: T[], changeRows: T[]) => {
    if (selected) {
      const newKeys = rows.map(getRowKey);
      handleSelectionChange(newKeys, rows);
    } else {
      handleSelectionChange([], []);
    }
  }, [getRowKey, handleSelectionChange]);

  const clearSelection = useCallback(() => {
    handleSelectionChange([], []);
  }, [handleSelectionChange]);

  const selectAll = useCallback((data: T[]) => {
    const keys = data.map(getRowKey);
    handleSelectionChange(keys, data);
  }, [getRowKey, handleSelectionChange]);

  const isSelected = useCallback((record: T): boolean => {
    const key = getRowKey(record);
    return selectedRowKeys.includes(key);
  }, [selectedRowKeys, getRowKey]);

  const getSelectedCount = useCallback((): number => {
    return selectedRowKeys.length;
  }, [selectedRowKeys]);

  const rowSelection = useMemo(() => ({
    selectedRowKeys,
    onChange: handleSelectionChange,
    onSelect: handleSelect,
    onSelectAll: handleSelectAll,
    getCheckboxProps,
  }), [selectedRowKeys, handleSelectionChange, handleSelect, handleSelectAll, getCheckboxProps]);

  return {
    selectedRowKeys,
    selectedRows,
    rowSelection,
    clearSelection,
    selectAll,
    isSelected,
    getSelectedCount,
  };
};
