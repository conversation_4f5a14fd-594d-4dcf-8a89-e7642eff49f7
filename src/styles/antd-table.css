/* Ant Design Table Custom Styles */

/* Table container */
.ant-table-wrapper {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Table header */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
  padding: 16px;
}

.ant-table-thead > tr > th:first-child {
  border-top-left-radius: 8px;
}

.ant-table-thead > tr > th:last-child {
  border-top-right-radius: 8px;
}

/* Table body */
.ant-table-tbody > tr > td {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.ant-table-tbody > tr:last-child > td {
  border-bottom: none;
}

/* Selection column */
.ant-table-selection-column {
  width: 48px !important;
  min-width: 48px !important;
}

/* Actions column */
.ant-table-cell .ant-btn {
  margin-right: 8px;
}

.ant-table-cell .ant-btn:last-child {
  margin-right: 0;
}

/* Pagination */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination .ant-pagination-total-text {
  color: #8c8c8c;
}

/* Empty state */
.ant-empty {
  padding: 40px 20px;
}

.ant-empty-description {
  color: #8c8c8c;
}

/* Loading overlay */
.ant-spin-container {
  position: relative;
}

.ant-spin-blur {
  opacity: 0.5;
  pointer-events: none;
}

/* Search input */
.ant-input-search {
  max-width: 300px;
}

/* Toolbar */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-toolbar-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.table-toolbar-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Status tags */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

/* Image preview */
.ant-image {
  border-radius: 4px;
  overflow: hidden;
}

.ant-image-img {
  transition: transform 0.2s;
}

.ant-image:hover .ant-image-img {
  transform: scale(1.05);
}

/* Responsive */
@media (max-width: 768px) {
  .ant-table-wrapper {
    overflow-x: auto;
  }
  
  .table-toolbar {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .table-toolbar-actions {
    justify-content: center;
  }
  
  .ant-pagination {
    text-align: center;
  }
}

/* Sticky header */
.ant-table-sticky-holder {
  background: #fafafa;
}

/* Ellipsis text */
.ant-typography-ellipsis {
  max-width: 200px;
}

/* Custom scrollbar */
.ant-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dropdown menu */
.ant-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-dropdown-menu-item {
  padding: 8px 12px;
}

.ant-dropdown-menu-item:hover {
  background: #f5f5f5;
}

/* Button styles */
.ant-btn-sm {
  height: 28px;
  padding: 0 8px;
  font-size: 12px;
}

.ant-btn-icon-only {
  width: 28px;
  padding: 0;
}

/* Table size variants */
.ant-table-small .ant-table-thead > tr > th,
.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px 12px;
}

.ant-table-large .ant-table-thead > tr > th,
.ant-table-large .ant-table-tbody > tr > td {
  padding: 20px 16px;
}

/* Fixed columns shadow */
.ant-table-cell-fix-left,
.ant-table-cell-fix-right {
  background: #fff;
}

.ant-table-cell-fix-left-last::after {
  box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
}

.ant-table-cell-fix-right-first::after {
  box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
}

/* Selection styles */
.ant-checkbox-wrapper {
  margin-right: 0;
}

.ant-table-row-selected > td {
  background: #e6f7ff;
}

.ant-table-row-selected:hover > td {
  background: #bae7ff;
}

/* Sorting styles */
.ant-table-column-sorter {
  color: #bfbfbf;
}

.ant-table-column-sorter-up.active,
.ant-table-column-sorter-down.active {
  color: #1890ff;
}

/* Filter styles */
.ant-table-filter-trigger {
  color: #bfbfbf;
}

.ant-table-filter-trigger.active {
  color: #1890ff;
}

/* Expandable row */
.ant-table-expand-icon-col {
  width: 48px;
}

.ant-table-row-expand-icon {
  border: 1px solid #d9d9d9;
  background: #fff;
  border-radius: 2px;
}

.ant-table-row-expand-icon:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}
