import { TransportServiceType } from "@/src/api/types/transport-method.types";
import { FileType, MediaFile } from "@/src/constants/file-types";
import { ExistingMediaFile } from "@/src/pages/dashboard/product/product-management/create/create";

export enum SyncServiceEnum {
  Sapo = "Sapo",
  KiotViet = "KiotViet",
  NhanhVN = "NhanhVN",
  Odoo = "Odoo",
}

export interface VariantType {
  itemsId: string;
  variantImage: {
    type: FileType;
    mediaFileId: string;
    link: string;
  };
  variantNameOne: string;
  variantValueOne: string;
  variantNameTwo: string;
  variantValueTwo: string;
  variantNameThree: string;
  variantValueThree: string;
  priceCapital: number;
  priceReal: number;
  price: number;
  quantity: number;
  quantityPurchase: number;
  errors?: {
    priceCapital?: string;
    price?: string;
    priceReal?: string;
    quantity?: string;
    quantityPurchase?: string;
  };
}

export interface SeoTagType {
  pageTitle: string;
  pageDesc: string;
  tags: string;
  url: string;
}

export interface ProductFormValues {
  // Basic info
  itemsId: string;
  itemsCode: string;
  itemsType: "Product" | "Service";
  itemsName: string;
  description: string;
  itemsInfo: string;
  // Categories
  categoryIds: [] | string[];

  // Display settings
  isVisible: boolean;
  isTop: boolean;
  // displayOrder: number;
  typePublish: "Publish" | "UnPublish";
  status: "Actived" | "Deleted";

  // Media
  images: ExistingMediaFile[];

  // Variants - Optional fields
  isVariant?: boolean;
  listVariant?: VariantType[];

  itemsPosition: number;

  // Pricing - Must follow the rule: PriceCapital ≤ Price ≤ PriceReal
  priceCapital: number; // Giá vốn (nhập hàng)
  price: number; // Giá bán thực tế (sau khuyến mãi)
  priceReal: number; // Giá niêm yết (trước khuyến mãi)

  // Quantity
  quantity: number;
  quantityPurchase: number;

  // Shipping Information
  warehouseId: string;
  itemsWeight: number;
  itemsLength: number;
  itemsWidth: number;
  itemsHeight: number;

  // SEO
  seoTags: SeoTagType[];

  // System fields
  created?: string;
  updated?: string;
  shopId?: string;
  partnerId?: string;

  // Purchase Quantity Settings
  quantityPurchaseMin: number;
  quantityPurchaseMax: number;

  // Display settings
  sold: number;

  //extraItemOptionGroups
  extraItemOptionGroups?: extraItemOptionGroupsType[];
  transportType: TransportServiceType[];
  customTaxRate?: number;

  // External source
  externalSource?: SyncServiceEnum | null;
}

export interface extraItemOptionGroupsType {
  itemOptionGroupId: string;
  itemOptionIds: string[];
}

export const initialProductValues: ProductFormValues = {
  itemsId: "",
  itemsCode: "",
  itemsType: "Product",
  itemsName: "",
  itemsInfo: "",
  description: "",
  categoryIds: [] as string[],
  isVisible: true,
  isTop: false,
  typePublish: "Publish",
  status: "Actived",
  images: [],
  isVariant: false,
  listVariant: [],
  itemsPosition: 0,
  priceCapital: 0,
  price: 0,
  priceReal: 0,
  quantity: 0,
  quantityPurchase: 0,
  warehouseId: "",
  itemsWeight: 0,
  itemsLength: 0,
  itemsWidth: 0,
  itemsHeight: 0,
  seoTags: [
    {
      pageTitle: "",
      pageDesc: "",
      tags: "",
      url: "",
    },
  ],
  quantityPurchaseMin: 1,
  quantityPurchaseMax: 0,
  sold: 0,
  extraItemOptionGroups: [],
  transportType: [],
  customTaxRate: null,
  externalSource: null,
};

export const validateVariant = (variant: VariantType): boolean => {
  return (
    variant.priceCapital > 0 &&
    variant.price > 0 &&
    variant.priceReal > 0 &&
    variant.priceCapital <= variant.price &&
    variant.price <= variant.priceReal
  );
};
