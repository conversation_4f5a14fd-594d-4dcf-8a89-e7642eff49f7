import React from 'react';
import {
  Edit,
  Trash2,
  Eye,
  Plus,
  RotateCcw,
  MoreHorizontal,
  Search,
  Download,
  Upload,
  Settings,
  Filter,
  SortAsc,
  SortDesc,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown,
  X,
  Check,
  AlertCircle,
  Info,
  CheckCircle,
  XCircle,
  Calendar,
  Clock,
  User,
  Users,
  Mail,
  Phone,
  MapPin,
  Star,
  Heart,
  Bookmark,
  Share,
  Copy,
  ExternalLink,
  Home,
  Building,
  ShoppingCart,
  CreditCard,
  Package,
  Truck,
  BarChart3,
  PieChart,
  TrendingUp,
  TrendingDown,
} from 'lucide-react';

interface IconProps {
  size?: number;
  className?: string;
  style?: React.CSSProperties;
}

// Action Icons
export const EditIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Edit size={size} {...props} />
);

export const DeleteIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Trash2 size={size} {...props} />
);

export const ViewIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Eye size={size} {...props} />
);

export const AddIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Plus size={size} {...props} />
);

export const RefreshIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <RotateCcw size={size} {...props} />
);

export const MoreIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <MoreHorizontal size={size} {...props} />
);

export const SearchIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Search size={size} {...props} />
);

// File Icons
export const DownloadIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Download size={size} {...props} />
);

export const UploadIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Upload size={size} {...props} />
);

// UI Icons
export const SettingsIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Settings size={size} {...props} />
);

export const FilterIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Filter size={size} {...props} />
);

export const SortAscIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <SortAsc size={size} {...props} />
);

export const SortDescIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <SortDesc size={size} {...props} />
);

// Navigation Icons
export const ChevronLeftIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <ChevronLeft size={size} {...props} />
);

export const ChevronRightIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <ChevronRight size={size} {...props} />
);

export const ChevronUpIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <ChevronUp size={size} {...props} />
);

export const ChevronDownIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <ChevronDown size={size} {...props} />
);

// Status Icons
export const CloseIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <X size={size} {...props} />
);

export const CheckIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Check size={size} {...props} />
);

export const WarningIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <AlertCircle size={size} {...props} />
);

export const InfoIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Info size={size} {...props} />
);

export const SuccessIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <CheckCircle size={size} {...props} />
);

export const ErrorIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <XCircle size={size} {...props} />
);

// Date & Time Icons
export const CalendarIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Calendar size={size} {...props} />
);

export const ClockIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Clock size={size} {...props} />
);

// User Icons
export const UserIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <User size={size} {...props} />
);

export const UsersIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Users size={size} {...props} />
);

// Contact Icons
export const MailIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Mail size={size} {...props} />
);

export const PhoneIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Phone size={size} {...props} />
);

export const LocationIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <MapPin size={size} {...props} />
);

// Social Icons
export const StarIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Star size={size} {...props} />
);

export const HeartIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Heart size={size} {...props} />
);

export const BookmarkIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Bookmark size={size} {...props} />
);

export const ShareIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Share size={size} {...props} />
);

export const CopyIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Copy size={size} {...props} />
);

export const ExternalLinkIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <ExternalLink size={size} {...props} />
);

// Business Icons
export const HomeIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Home size={size} {...props} />
);

export const BuildingIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Building size={size} {...props} />
);

export const ShoppingCartIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <ShoppingCart size={size} {...props} />
);

export const CreditCardIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <CreditCard size={size} {...props} />
);

export const PackageIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Package size={size} {...props} />
);

export const TruckIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <Truck size={size} {...props} />
);

// Chart Icons
export const BarChartIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <BarChart3 size={size} {...props} />
);

export const PieChartIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <PieChart size={size} {...props} />
);

export const TrendingUpIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <TrendingUp size={size} {...props} />
);

export const TrendingDownIcon: React.FC<IconProps> = ({ size = 16, ...props }) => (
  <TrendingDown size={size} {...props} />
);

// Icon mapping for easy access
export const Icons = {
  // Actions
  edit: EditIcon,
  delete: DeleteIcon,
  view: ViewIcon,
  add: AddIcon,
  refresh: RefreshIcon,
  more: MoreIcon,
  search: SearchIcon,
  
  // Files
  download: DownloadIcon,
  upload: UploadIcon,
  
  // UI
  settings: SettingsIcon,
  filter: FilterIcon,
  sortAsc: SortAscIcon,
  sortDesc: SortDescIcon,
  
  // Navigation
  chevronLeft: ChevronLeftIcon,
  chevronRight: ChevronRightIcon,
  chevronUp: ChevronUpIcon,
  chevronDown: ChevronDownIcon,
  
  // Status
  close: CloseIcon,
  check: CheckIcon,
  warning: WarningIcon,
  info: InfoIcon,
  success: SuccessIcon,
  error: ErrorIcon,
  
  // Date & Time
  calendar: CalendarIcon,
  clock: ClockIcon,
  
  // Users
  user: UserIcon,
  users: UsersIcon,
  
  // Contact
  mail: MailIcon,
  phone: PhoneIcon,
  location: LocationIcon,
  
  // Social
  star: StarIcon,
  heart: HeartIcon,
  bookmark: BookmarkIcon,
  share: ShareIcon,
  copy: CopyIcon,
  externalLink: ExternalLinkIcon,
  
  // Business
  home: HomeIcon,
  building: BuildingIcon,
  shoppingCart: ShoppingCartIcon,
  creditCard: CreditCardIcon,
  package: PackageIcon,
  truck: TruckIcon,
  
  // Charts
  barChart: BarChartIcon,
  pieChart: PieChartIcon,
  trendingUp: TrendingUpIcon,
  trendingDown: TrendingDownIcon,
} as const;

export type IconName = keyof typeof Icons;
