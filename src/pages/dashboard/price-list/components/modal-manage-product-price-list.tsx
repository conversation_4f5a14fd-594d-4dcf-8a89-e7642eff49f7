import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  IconButton,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Paper,
  Avatar,
  TablePagination,
  Tooltip,
  Dialog,
  Tabs,
  Tab,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  MenuItem,
  Select,
  InputAdornment,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import DeleteIcon from "@mui/icons-material/Delete";
import { Add, Delete, DeleteOutline, Search } from "@mui/icons-material";
import {
  GetListProductParam,
  PriceListDto,
  PriceListItemDto,
} from "@/src/api/services/price-list/price-list.service";
import { useStoreId } from "@/src/hooks/use-store-id";
import { usePriceList } from "@/src/api/hooks/price-list/use-price-list";
import { rowPerPageOptionsDefault } from "@/src/types/store/enum-type";
import { useDebounce } from "@/src/hooks/use-debounce";
import { formatPrice } from "@/src/api/types/membership.types";
import { GetProductCategoryRequest, TreeCategory } from "@/src/api/types/product-category.types";
import { StorageService } from "nextjs-api-lib";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import { getCategoryName } from "../../product/product-management/product-management";
import ZoomImageDialog from "@/src/components/zoom-image";
import ModalAddProductToPriceList from "./modal-add-product-to-price-list";
import TruncatedText from "@/src/components/truncated-text/truncated-text";
import { useValidImage } from "@/src/hooks/useValidImage";
import { useAppSelector } from "@/src/redux/hooks";
import { CURRENCY_UNIT } from "@/src/constants/constant";

export interface ModalManageProductPriceListProps {
  onClose: () => void;
  isOpenModalListProduct: boolean;
  setIsOpenModalListProduct: any;
  isOpenModalManageProduct: boolean;
  setIsOpenModalManageProduct: any;
  priceListManage: PriceListDto;
}
export const renderCategoryOptions = (categories: TreeCategory[], level = 0) =>
  categories.map((cat) => [
    <MenuItem key={cat.categoryId} value={cat.categoryId} sx={{ pl: 2 + level * 2, width: 250 }}>
      <TruncatedText text={cat.categoryName} />
    </MenuItem>,
    ...(cat.listSubCategory?.length ? renderCategoryOptions(cat.listSubCategory, level + 1) : []),
  ]);
const ModalManageProductPriceList: React.FC<ModalManageProductPriceListProps> = ({
  onClose,
  isOpenModalListProduct,
  setIsOpenModalListProduct,
  isOpenModalManageProduct,
  setIsOpenModalManageProduct,
  priceListManage,
}) => {
  const { getListProductByPriceListId, getListProductNotInPriceList, deleteProductsForPriceList } =
    usePriceList();

  const [listProductInPriceList, setListProductInPriceList] = useState<PriceListItemDto[]>([]);

  const [selected, setSelected] = useState<string[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [pageItem, setPageItem] = useState(0);

  const [rowsPerPageItem, setRowsPerPageItem] = useState(10);
  const [searchItem, setSearchItem] = useState<string>("");

  const [search, setSearch] = useState("");
  const [totalCount, setTotalCount] = useState<number>(0);
  const [totalCountItem, setTotalCountItem] = useState<number>(0);
  const [tabValue, setTabValue] = useState(0);
  const storeId = useStoreId();
  const [listProductNotInPriceList, setListProductNotInPriceList] = useState<PriceListItemDto[]>(
    []
  );
  const [selectedItemDelete, setSelectedItemDelete] = useState<string>();
  const [openZoom, setOpenZoom] = useState<boolean>(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [categories, setCategories] = useState<TreeCategory[]>([]);
  const [isOpenModalDeleteItem, setIsOpenModalDeleteItem] = useState<boolean>(false);
  const [isOpenModalDeleteMany, setIsOpenModalDeleteMany] = useState<boolean>(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");
  const [selectedCategoryIdNotIn, setSelectedCategoryIdNotIn] = useState<string>("");
  const currentShop = useAppSelector((state) => state.shop.currentShop);

  const { getProductCategoryTree } = useProductCategory();

  const debounceSearchValue = useDebounce(search, 500);
  const debounceSearchItemValue = useDebounce(searchItem, 500);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setPage(0);
    setSearch("");
    setSelected([]);
  };

  const fetchCategories = async () => {
    try {
      const params: GetProductCategoryRequest = {
        categoryType: tabValue === 0 ? "Product" : "Service",
        shopId: storeId,
        partnerId: StorageService.get("partnerId") as string | null,
        paging: {
          name: "Created",
          nameType: "Created",
          sortType: "desc",
          sort: "desc",
          pageIndex: 0,
          pageSize: 100,
        },
      };
      const response = await getProductCategoryTree(params);
      setCategories(response.data.data.result || []);
    } catch (error) {}
  };
  const fetchListProductByPriceList = async () => {
    const data: GetListProductParam = {
      shopId: storeId,
      itemsType: tabValue === 0 ? "Product" : "Service",
      priceListId: priceListManage?.priceListId,
      categoryId: selectedCategoryId,
      paging: {
        PageIndex: page,
        PageSize: rowsPerPage,
        Name: "Created",
        NameType: "Created",
        Sort: "desc",
        SortType: "desc",
        Search: debounceSearchValue,
      },
    };
    const res = await getListProductByPriceListId(data);
    if (res?.status === 200) {
      setListProductInPriceList(res?.data?.data?.result);
      setTotalCount(res?.data?.data?.total);
    }
  };
  useEffect(() => {
    fetchListProductByPriceList();
    fetchCategories();
  }, [
    storeId,
    priceListManage,
    isOpenModalManageProduct,
    debounceSearchValue,
    tabValue,
    page,
    rowsPerPage,
    selectedCategoryId,
  ]);

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(listProductInPriceList.map((row) => row.itemsId));
    } else {
      setSelected([]);
    }
  };

  const handleSelectOne = (id: string) => {
    setSelected((prev) => (prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id]));
  };

  const fetchListProductNotInPriceList = async () => {
    const data: GetListProductParam = {
      shopId: storeId,
      itemsType: tabValue === 0 ? "Product" : "Service",
      priceListId: priceListManage?.priceListId,
      categoryId: selectedCategoryIdNotIn,
      paging: {
        PageIndex: pageItem,
        PageSize: rowsPerPageItem,
        Name: "Created",
        NameType: "Created",
        Sort: "desc",
        SortType: "desc",
        Search: debounceSearchItemValue,
      },
    };
    const res = await getListProductNotInPriceList(data);
    if (res?.status === 200) {
      setListProductNotInPriceList(res?.data?.data?.result);
      setTotalCountItem(res?.data?.data?.total);
    }
  };

  useEffect(() => {
    if (
      isOpenModalListProduct &&
      Array.isArray(listProductInPriceList) &&
      listProductInPriceList.length > 0
    ) {
      setSelected(listProductInPriceList.map((item) => item.itemsId));
    }
  }, [isOpenModalListProduct, listProductInPriceList]);

  const handleDeleteItemForPriceList = async () => {
    const res = await deleteProductsForPriceList(storeId, priceListManage?.priceListId, [
      selectedItemDelete,
    ]);
    if (res?.status === 200) {
      setIsOpenModalDeleteItem(false);
      fetchListProductByPriceList();
    }
  };
  const handleDeleteManyItemForPriceList = async () => {
    const res = await deleteProductsForPriceList(storeId, priceListManage?.priceListId, selected);
    if (res?.status === 200) {
      setIsOpenModalDeleteMany(false);
      setSelected([]);
      fetchListProductByPriceList();
    }
  };

  return (
    <>
      <Dialog
        open={isOpenModalManageProduct}
        onClose={() => {
          setIsOpenModalManageProduct(false);
          setTabValue(0);
          setSelectedCategoryId("");
          setSelectedCategoryIdNotIn("");
          setSearch("");
        }}
        maxWidth="lg"
        fullWidth
      >
        <Box
          sx={{
            bgcolor: "#fff",
            borderRadius: 2,
            p: 3,
            boxShadow: 3,
            minWidth: 900,
            position: "relative",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Typography variant="h6" sx={{ flex: 1, fontWeight: 700 }}>
              Quản lý sản phẩm trong bảng giá
            </Typography>

            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>

          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{
              mb: 3,
              "& .MuiTabs-indicator": {
                background: "#2654FE",
              },
            }}
          >
            <Tab
              sx={{
                textTransform: "none !important",
                color: "#000",
                "&.Mui-selected": {
                  color: "#2654FE !important",
                  height: "3px",
                },
              }}
              value={0}
              label={"Sản phẩm"}
            />
            {/* <Tab
              value={1}
              sx={{
                textTransform: "none !important",
                color: "#000",
                "&.Mui-selected": {
                  color: "#2654FE !important",
                  height: "3px",
                },
              }}
              label={"Dịch vụ"}
            /> */}
          </Tabs>

          <Box sx={{ display: "flex", gap: 2, mb: 2, justifyContent: "space-between" }}>
            <Box>
              <Select
                displayEmpty
                sx={{
                  width: "240px",
                  height: "40px",
                  mr: 2,
                  pr: 4,
                  "& .MuiSelect-select": {
                    height: "45px",
                    display: "flex",
                    alignItems: "center",
                    paddingTop: 0,
                    paddingBottom: 0,
                  },
                }}
                labelId="category-select-label"
                value={selectedCategoryId}
                label="Chọn danh mục"
                onChange={(e) => {
                  setPage(0);
                  setSelectedCategoryId(e.target.value);
                }}
              >
                <MenuItem value="">Tất cả danh mục</MenuItem>
                {renderCategoryOptions(categories)}
              </Select>
              <TextField
                sx={{
                  width: 240,
                  height: 40,
                  mr: 2,
                  bgcolor: "#fafafa",
                  "& .MuiInputBase-root": {
                    height: 40,
                    minHeight: 40,
                    boxSizing: "border-box",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  },
                  "& input": {
                    height: 40,
                    minHeight: 40,
                    boxSizing: "border-box",
                    padding: 0,
                    display: "flex",
                    alignItems: "center",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  },
                }}
                size="small"
                placeholder="Tìm kiếm sản phẩm"
                value={search}
                onChange={(e) => {
                  setPage(0);
                  setSearch(e.target.value);
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            <Box>
              {selected?.length > 0 && (
                <Button
                  variant="outlined"
                  color="error"
                  sx={{ ml: 2 }}
                  onClick={() => {
                    setIsOpenModalDeleteMany(true);
                  }}
                >
                  <DeleteOutline sx={{ mr: 0.5, fontSize: 20 }} />
                  Xoá ({selected?.length})
                </Button>
              )}
              <Button
                variant="contained"
                sx={{ bgcolor: "#2257FF", ml: 2, "&:hover": { bgcolor: "#1a44c8" } }}
                onClick={() => {
                  fetchListProductNotInPriceList();
                  setIsOpenModalManageProduct(false);
                  setIsOpenModalListProduct(true);
                  setSelectedCategoryIdNotIn("");
                }}
              >
                <Add sx={{ mr: 0.5 }} />
                Thêm sản phẩm
              </Button>
            </Box>
          </Box>

          <TableContainer component={Paper} sx={{ borderRadius: 1, boxShadow: "none" }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      indeterminate={
                        selected.length > 0 && selected.length < listProductInPriceList.length
                      }
                      checked={
                        listProductInPriceList.length > 0 &&
                        selected.length === listProductInPriceList.length
                      }
                      onChange={handleSelectAll}
                      inputProps={{ "aria-label": "select all products" }}
                    />
                  </TableCell>
                  <TableCell align="center" sx={{ width: 5, fontSize: 15 }}>
                    STT
                  </TableCell>
                  <TableCell sx={{ minWidth: "150px", fontSize: 15 }}>Mã sản phẩm</TableCell>
                  <TableCell sx={{ minWidth: "300px", fontSize: 15 }}>Tên sản phẩm</TableCell>
                  <TableCell sx={{ minWidth: "150px", fontSize: 15 }}>Danh mục</TableCell>
                  <TableCell sx={{ minWidth: "150px", fontSize: 15 }} align="right">
                    Giá bán
                  </TableCell>
                  <TableCell sx={{ minWidth: "200px", fontSize: 15 }} align="right">
                    Giá sau điều chỉnh
                  </TableCell>
                  <TableCell
                    sx={{
                      minWidth: "100px",
                      fontSize: 15,
                      position: "sticky",
                      right: 0,
                      bottom: 0,
                      backgroundColor: "#fff",
                      zIndex: 3,
                      boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
                      padding: { xs: "16px 4px", sm: "20px 16px" },
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "center",
                      width: { xs: "70px", sm: "90px" },
                    }}
                    align="center"
                  >
                    Quản lý
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {Array.isArray(listProductInPriceList) && listProductInPriceList.length > 0 ? (
                  listProductInPriceList.map((row, index) => {
                    return (
                      <RenderItem
                        row={row}
                        index={index}
                        categories={categories}
                        setSelectedItemDelete={setSelectedItemDelete}
                        setIsOpenModalDeleteItem={setIsOpenModalDeleteItem}
                        handleSelectOne={handleSelectOne}
                        page={page}
                        rowsPerPage={rowsPerPage}
                        selected={selected}
                        setOpenZoom={setOpenZoom}
                        setSelectedImage={setSelectedImage}
                        currentShop={currentShop}
                      />
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell
                      sx={{
                        paddingTop: 4,
                        fontSize: { xs: "0.875rem", sm: "1rem" },
                        fontWeight: 400,
                        color: "#222",
                      }}
                      colSpan={9}
                      align="center"
                    >
                      Không có dữ liệu
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            <TablePagination
              component="div"
              count={totalCount}
              page={page}
              onPageChange={(_, newPage) => setPage(newPage)}
              rowsPerPage={rowsPerPage}
              onRowsPerPageChange={(e) => {
                setRowsPerPage(parseInt(e.target.value, 10));
                setPage(0);
              }}
              rowsPerPageOptions={rowPerPageOptionsDefault}
              labelRowsPerPage="Số dòng trên trang:"
            />
          </TableContainer>
        </Box>
      </Dialog>
      <ModalAddProductToPriceList
        isOpenModalListProduct={isOpenModalListProduct}
        onClose={() => {
          setIsOpenModalListProduct(false);
          setSelected([]);
        }}
        isOpenModalManageProduct={isOpenModalManageProduct}
        setIsOpenModalManageProduct={setIsOpenModalManageProduct}
        selected={selected}
        setSelected={setSelected}
        handleSelectOne={handleSelectOne}
        priceListManage={priceListManage}
        listProductNotInPriceList={listProductNotInPriceList}
        setListProductNotInPriceList={setListProductNotInPriceList}
        totalCountItem={totalCountItem}
        setTotalCountItem={setTotalCountItem}
        page={pageItem}
        setPage={setPageItem}
        rowsPerPage={rowsPerPageItem}
        setRowsPerPage={setRowsPerPageItem}
        fetchListProductNotInPriceList={fetchListProductNotInPriceList}
        fetchListProductByPriceList={fetchListProductByPriceList}
        debounceSearchItemValue={debounceSearchItemValue}
        search={searchItem}
        setSearch={setSearchItem}
        tabValue={tabValue}
        setTabValue={setTabValue}
        categories={categories}
        selectedCategoryIdNotIn={selectedCategoryIdNotIn}
        setSelectedCategoryIdNotIn={setSelectedCategoryIdNotIn}
      />
      <ZoomImageDialog
        onClose={() => {
          setSelectedImage(null);
          setOpenZoom(false);
        }}
        open={openZoom}
        src={selectedImage || ""}
        alt={"Zoomed category image"}
        maxHeight="80vh"
      />

      <Dialog
        open={isOpenModalDeleteItem}
        onClose={() => setIsOpenModalDeleteItem(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{`Xác nhận xoá sản phẩm `}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {`Bạn có chắc chắn muốn xoá sản phẩm khỏi bảng giá này không?`}
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ mx: 2, my: 2 }}>
          <Button
            onClick={() => setIsOpenModalDeleteItem(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Hủy
          </Button>
          <Button
            onClick={handleDeleteItemForPriceList}
            variant="contained"
            autoFocus
            sx={{ minWidth: 100 }}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={isOpenModalDeleteMany}
        onClose={() => setIsOpenModalDeleteMany(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>{`Xác nhận xoá sản phẩm `}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {`Bạn có chắc chắn muốn xoá ${selected?.length} sản phẩm khỏi bảng giá này không?`}
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ mx: 2, my: 2 }}>
          <Button
            onClick={() => setIsOpenModalDeleteMany(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Hủy
          </Button>
          <Button
            onClick={handleDeleteManyItemForPriceList}
            variant="contained"
            autoFocus
            sx={{ minWidth: 100 }}
          >
            Xác nhận
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ModalManageProductPriceList;

const RenderItem = ({
  row,
  index,
  selected,
  handleSelectOne,
  page,
  rowsPerPage,
  setSelectedImage,
  setOpenZoom,
  categories,
  setSelectedItemDelete,
  setIsOpenModalDeleteItem,
  currentShop,
}) => {
  const imgItemSrc = useValidImage(row?.images?.[0]?.link, currentShop?.shopLogo?.link);
  const imgVariantSrc = useValidImage(row.variantImage?.link, imgItemSrc);
  return (
    <TableRow key={row.itemsId}>
      <TableCell padding="checkbox">
        <Checkbox
          checked={selected.includes(row.itemsId)}
          onChange={() => handleSelectOne(row.itemsId)}
          inputProps={{ "aria-label": `select product ${row.itemsId}` }}
        />
      </TableCell>
      <TableCell align="center">{page * rowsPerPage + index + 1}</TableCell>
      <TableCell>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1, fontSize: 15 }}>
          {row.itemsCode}
        </Box>
      </TableCell>
      <TableCell sx={{ maxWidth: "300px" }}>
        <Box
          sx={{
            display: "flex",
            alignItems: "flex-start",
            gap: 1,
            width: "100%",
          }}
        >
          <Box
            sx={{
              position: "relative",
              display: "inline-block",
              "&:hover .avatar-hover-opacity": {
                opacity: 0.9,
                transition: "opacity 0.2s",
              },
            }}
          >
            <Avatar
              className="avatar-hover-opacity"
              onClick={() => {
                const imageLink =
                  row.isVariant === true ? row?.variantImage?.link : row?.images?.[0]?.link;

                if (imageLink) {
                  setSelectedImage(imageLink);
                  setOpenZoom(true);
                }
              }}
              src={imgVariantSrc}
              sx={{
                width: 34,
                height: 34,
                bgcolor: "#e0e0e0",
                cursor: row.variantImage?.link || (row?.images?.[0]?.link && "pointer"),
                transition: "opacity 0.2s",
              }}
            />
          </Box>
          <Box sx={{ display: "flex", flexDirection: "column", width: "80%" }}>
            <TruncatedText
              typographyProps={{ fontSize: 15, fontWeight: 500 }}
              width="100%"
              text={row.itemsName}
            />
            <Typography
              sx={{
                fontSize: 14,
                display: "flex",
                flexDirection: "column",
                width: "100%",
              }}
            >
              {row.isVariant === true && (
                <>
                  {row.variantNameOne && row.variantValueOne && (
                    <span style={{ display: "flex", alignItems: "center" }}>
                      <TruncatedText
                        typographyProps={{ fontSize: 14 }}
                        text={row.variantNameOne}
                        width="100%"
                      />
                      :
                      <TruncatedText
                        typographyProps={{
                          fontSize: 14,
                          pl: 0.4,
                        }}
                        width="100%"
                        text={row.variantValueOne}
                      />
                    </span>
                  )}
                  {row.variantNameTwo && row.variantValueTwo && (
                    <span style={{ display: "flex", alignItems: "center" }}>
                      <TruncatedText
                        typographyProps={{ fontSize: 14 }}
                        width="100%"
                        text={row.variantNameTwo}
                      />
                      :
                      <TruncatedText
                        text={row.variantValueTwo}
                        typographyProps={{
                          fontSize: 14,
                          pl: 0.4,
                        }}
                        width="100%"
                      />
                    </span>
                  )}
                  {row.variantNameThree && row.variantValueThree && (
                    <span style={{ display: "flex", alignItems: "center" }}>
                      <TruncatedText
                        typographyProps={{ fontSize: 14 }}
                        text={row.variantNameThree}
                        width="100%"
                      />
                      :
                      <TruncatedText
                        text={row.variantValueThree}
                        typographyProps={{
                          fontSize: 14,
                          pl: 0.4,
                        }}
                        width="100%"
                      />
                    </span>
                  )}
                </>
              )}
            </Typography>
          </Box>
        </Box>
      </TableCell>
      <TableCell sx={{ fontSize: 15 }}>
        <TruncatedText
          typographyProps={{ fontSize: 15 }}
          text={getCategoryName(row?.categoryIds, categories)}
          width="100%"
        />
      </TableCell>
      <TableCell sx={{ fontSize: 15 }} align="right">
        {formatPrice(row.price)}
        {CURRENCY_UNIT}
      </TableCell>
      <TableCell sx={{ fontSize: 15 }} align="right">
        {formatPrice(row.adjustedPrice)}
        {CURRENCY_UNIT}
      </TableCell>
      <TableCell
        align="center"
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "sticky",
          right: 0,
          backgroundColor: "#fff",
          zIndex: 2,
          padding: "35px 16px",
          height: "100%",
          boxShadow: "-2px 0 5px rgba(0,0,0,0.1)",
        }}
      >
        <Tooltip title="Xóa">
          <IconButton
            color="error"
            onClick={() => {
              setSelectedItemDelete(row?.itemsId);
              setIsOpenModalDeleteItem(true);
            }}
          >
            <DeleteIcon />
          </IconButton>
        </Tooltip>
      </TableCell>
    </TableRow>
  );
};
