import "simplebar-react/dist/simplebar.min.css";
import "mapbox-gl/dist/mapbox-gl.css";
import Head from "next/head";
import { Provider as ReduxProvider } from "react-redux";
import { CacheProvider } from "@emotion/react";
import CssBaseline from "@mui/material/CssBaseline";
import { ThemeProvider } from "@mui/material/styles";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AntdRegistry } from "@ant-design/nextjs-registry";
import { ConfigProvider } from "antd";
import viVN from "antd/locale/vi_VN";

import { RTL } from "src/components/rtl";
import { SplashScreen } from "src/components/splash-screen";
import { SettingsButton } from "src/components/settings/settings-button";
import { SettingsDrawer } from "src/components/settings/settings-drawer";
import { Toaster } from "src/components/toaster";
import { gtmConfig } from "src/config";
import { AuthConsumer, AuthProvider } from "src/contexts/auth";
import { SettingsConsumer, SettingsProvider } from "src/contexts/settings";
import { useAnalytics } from "src/hooks/use-analytics";
import { useNprogress } from "src/hooks/use-nprogress";
import { store } from "src/store/index";
import { createTheme } from "src/theme";
import { createEmotionCache } from "src/utils/create-emotion-cache";
import { NotificationProvider } from "../contexts/NotificationContext";
import { MainLayout } from "src/components/main-layout";
import { useRouter } from "next/router";

import SnackbarProvider from "@/src/components/snackbar/snackbar-provider";
import { SnackbarUtilsConfigurator } from "@/src/utils/snackbar";
import "src/styles/globals.css";
import "src/styles/style.css";
import "src/styles/antd-table.css";
// Remove if locales are not used
import "src/locales/i18n";

import "../styles/style.css";
import PWAInstallPrompt from "src/components/pwaInstallPromt";

const clientSideEmotionCache = createEmotionCache();

const CustomApp = (props) => {
  const { Component, emotionCache = clientSideEmotionCache, pageProps } = props;
  const router = useRouter();

  useAnalytics(gtmConfig);
  useNprogress();

  // Use the layout defined at the page level, or fallback to passing the page
  const getLayout = Component.getLayout ?? ((page) => page);

  // Check if the current page is a dashboard page
  // Use router.pathname for both server-side and client-side rendering
  const isDashboardPage = Component.isDashboardPage || router.pathname.startsWith("/dashboard");

  return (
    <CacheProvider value={emotionCache}>
      <Head>
        <title>Evotech</title>
        <meta name="viewport" content="initial-scale=1, width=device-width" />
      </Head>
      <ReduxProvider store={store}>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <SnackbarProvider anchorOrigin={{ vertical: "top", horizontal: "right" }}>
            <SnackbarUtilsConfigurator />
            <AuthProvider>
              <AuthConsumer>
                {(auth) => (
                  <SettingsProvider>
                    <SettingsConsumer>
                      {(settings) => {
                        // Prevent theme flicker when restoring custom settings from browser storage
                        if (!settings.isInitialized) {
                          // return null;
                        }

                        const theme = createTheme({
                          colorPreset: settings.colorPreset,
                          contrast: settings.contrast,
                          direction: settings.direction,
                          paletteMode: settings.paletteMode,
                          responsiveFontSizes: settings.responsiveFontSizes,
                        });

                        // Prevent guards from redirecting
                        const showSlashScreen = !auth.isInitialized;

                        return (
                          <ThemeProvider theme={theme}>
                            <Head>
                              <meta name="color-scheme" content={settings.paletteMode} />
                              <meta name="theme-color" content={theme.palette.neutral[900]} />
                            </Head>
                            <RTL direction={settings.direction}>
                              <CssBaseline />
                              <AntdRegistry>
                                <ConfigProvider
                                  locale={viVN}
                                  theme={{
                                    token: {
                                      colorPrimary: "#2654FE",
                                      borderRadius: 6,
                                    },
                                  }}
                                >
                                  {showSlashScreen ? (
                                    <SplashScreen />
                                  ) : (
                                    <>
                                      <NotificationProvider>
                                        {/* If it's a dashboard page, wrap it with MainLayout */}
                                        {isDashboardPage ? (
                                          <MainLayout>
                                            {getLayout(<Component {...pageProps} />)}
                                          </MainLayout>
                                        ) : (
                                          getLayout(<Component {...pageProps} />)
                                        )}
                                        <SettingsDrawer
                                          canReset={settings.isCustom}
                                          onClose={settings.handleDrawerClose}
                                          onReset={settings.handleReset}
                                          onUpdate={settings.handleUpdate}
                                          open={settings.openDrawer}
                                          values={{
                                            colorPreset: settings.colorPreset,
                                            contrast: settings.contrast,
                                            direction: settings.direction,
                                            paletteMode: settings.paletteMode,
                                            responsiveFontSizes: settings.responsiveFontSizes,
                                            stretch: settings.stretch,
                                            layout: settings.layout,
                                            navColor: settings.navColor,
                                          }}
                                        />
                                      </NotificationProvider>
                                    </>
                                  )}
                                  <Toaster />
                                </ConfigProvider>
                              </AntdRegistry>
                            </RTL>
                          </ThemeProvider>
                        );
                      }}
                    </SettingsConsumer>
                  </SettingsProvider>
                )}
              </AuthConsumer>
            </AuthProvider>
          </SnackbarProvider>
        </LocalizationProvider>
      </ReduxProvider>
      <PWAInstallPrompt />
    </CacheProvider>
  );
};

export default CustomApp;
