import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Box,
  Typography,
  Select,
  MenuItem,
  Paper,
  TextField,
  InputAdornment,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from "@mui/material";
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  NavigateBefore as NavigateBeforeIcon,
  NavigateNext as NavigateNextIcon,
  Search as SearchIcon,
  FileDownload as FileDownloadIcon,
  ChevronLeft,
  ChevronRight,
} from "@mui/icons-material";
import DownloadIcon from "@mui/icons-material/Download";
import { on } from "events";
import { DatePicker, DateTimePicker, LocalizationProvider } from "@mui/x-date-pickers";
import Link from "next/link";
import { Calendar } from "@untitled-ui/icons-react";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { margin } from "@mui/system";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { DateRangePicker } from "@mui/x-date-pickers-pro/DateRangePicker";
import { useZaloAutomation } from "@/src/api/hooks/zalo-automation/zalo-automation";
import { useStoreId } from "@/src/hooks/use-store-id";
import { logger } from "@/src/utils/logger";
import {
  CURRENCY_UNIT,
  LIMIT,
  listStatusNotiHistory,
  SKIP,
  statusOptions,
  SUCCESS_STATUS,
  ZALO_HISTORY_STATUS_ENUM,
  ZALO_NOTI_HISTORY_ENUM,
} from "@/src/constants/constant";
import dayjs, { Dayjs } from "dayjs";
import useSnackbar from "@/src/hooks/use-snackbar";
import { formatPrice } from "@/src/api/types/membership.types";
import { useTriggerEventHistory } from "@/src/api/hooks/trigger-event-history/use-trigger-event-history";
import {
  ParamGetListTriggerEventHistory,
  TriggerEventHistoryDto,
} from "@/src/api/services/trigger-event-history/trigger-event-history.service";
import { useTriggerEvent } from "@/src/api/hooks/trigger-event/use-trigger-event";
import { TriggerEventDto } from "@/src/api/services/trigger-event/trigger-event.service";

interface ReportAutomationDto {
  ShopId: string;
  FromDate: string;
  ToDate: string;
  Status: string;
  TriggerEventCode?: string;
  SearchKeyword: string;
  Paging: {
    PageIndex: number;
    PageSize: number;
  };
}

const getStatusConfig = (status: string) => {
  switch (status) {
    case "Success":
      return {
        color: "rgb(34, 154, 22)",
        backgroundColor: "rgba(84, 214, 44, 0.16)",
      };
    default:
      return {
        color: "rgb(183, 33, 54)",
        backgroundColor: "rgba(255, 72, 66, 0.16)",
      };
  }
};

const Report = () => {
  const snackbar = useSnackbar();
  const { getMessageReport, exportSentMessageReport } = useZaloAutomation();
  const { getListHistorySendMessageByTriggerEvent } = useTriggerEventHistory();
  const { getListTriggerEvent } = useTriggerEvent();
  const [listTriggerEventHistory, setListTriggerEventHistory] = useState<TriggerEventHistoryDto[]>(
    []
  );
  const [total, setTotal] = useState<number>();
  const [selected, setSelected] = useState<number[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [isOpenModalCreate, setIsOpenModalCreate] = useState<boolean>(false);
  const [campaignType, setCampaignType] = useState("oneTime");
  const [isDetail, setIsDetail] = useState(false);
  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalUpdate, setIsOpenModalUpdate] = useState(false);
  const [open, setOpen] = useState(false);
  const storeId = useStoreId();
  const [listTriggerEvent, setListTriggerEvent] = useState<TriggerEventDto[]>();

  const [filterData, setFilterData] = useState<ReportAutomationDto>({
    ShopId: storeId,
    FromDate: dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
    ToDate: dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
    Status: "all",
    TriggerEventCode: "all",
    SearchKeyword: "",
    Paging: {
      PageIndex: SKIP,
      PageSize: LIMIT,
    },
  });

  const fetchListTriggerEvent = async () => {
    const res = await getListTriggerEvent();
    if (res && res?.status === 200) {
      if (Array.isArray(res?.data?.data) && res?.data?.data.length > 0) {
        setListTriggerEvent(res?.data?.data);
      }
    }
  };
  useEffect(() => {
    fetchListTriggerEvent();
  }, [storeId]);

  useEffect(() => {
    fetchMessageReport();
  }, [filterData, storeId]);

  const fetchMessageReport = async () => {
    try {
      const data: ParamGetListTriggerEventHistory = {
        ShopId: storeId,
        ChannelType: filterData.Status === "all" ? "" : filterData.Status,
        Search: filterData.SearchKeyword,
        TriggerEventCode: filterData.TriggerEventCode === "all" ? "" : filterData.TriggerEventCode,
        FromDate: filterData.FromDate,
        ToDate: filterData.ToDate,
        Paging: {
          NameType: "Created",
          SortType: "asc",
          Name: "Created",
          Sort: "asc",
          PageSize: filterData.Paging.PageSize,
          PageIndex: filterData.Paging.PageIndex,
          Search: filterData.SearchKeyword,
        },
      };
      const res = await getListHistorySendMessageByTriggerEvent(data);

      if (res?.status === 200) {
        setListTriggerEventHistory(res?.data?.data?.result || []);
        setTotal(res?.data?.data?.total || 0);
      }
    } catch (error) {
      logger.error("Error fetching shops:", error);
    }
  };
  const handleSelectAllClick = (event) => {
    //   if (event.target.checked) {
    //     const newSelected = messageReports.map((n) => n.id);
    //     setSelected(newSelected);
    //     return;
    //   }
    //   setSelected([]);
  };

  const handleDateRangeChange = (newValue: [Dayjs | null, Dayjs | null]) => {
    setPage(0);
    const [start, end] = newValue;

    let fromDate = start ? start.startOf("day").format("YYYY-MM-DD HH:mm:ss") : filterData.FromDate;
    let toDate = end ? end.endOf("day").format("YYYY-MM-DD HH:mm:ss") : filterData.ToDate;

    if (fromDate !== "Invalid Date" && toDate !== "Invalid Date") {
      if (start && end && start.isAfter(dayjs(toDate))) {
        toDate = start.endOf("day").format("YYYY-MM-DD HH:mm:ss");
      }

      setFilterData((prev) => ({
        ...prev,
        FromDate: fromDate,
        ToDate: toDate,
        Paging: { ...prev.Paging, PageIndex: SKIP },
      }));
    }
  };

  const handleChangeRowsPerPage = (event) => {
    setPage(0);
    const newPageSize = Number(event.target.value);
    setFilterData((prev) => ({
      ...prev,
      Paging: {
        PageIndex: prev.Paging.PageIndex,
        PageSize: newPageSize,
      },
    }));
  };
  const searchFieldProps = {
    placeholder: "Nhập tên người nhận",
    variant: "outlined" as "outlined",
    size: "small" as "small",
    sx: {
      width: { xs: "100%", sm: 240 },
      "& .MuiOutlinedInput-root": {
        borderRadius: 30,
        backgroundColor: "#fff",
      },
      marginRight: 1,
    },
    InputProps: {
      startAdornment: (
        <InputAdornment position="start">
          <SearchIcon fontSize="small" />
        </InputAdornment>
      ),
    },
  };

  const importButtonProps = {
    variant: "outlined" as "outlined",
    startIcon: <FileDownloadIcon />,
    sx: {
      textTransform: "none",
      width: { xs: "100%", sm: "auto" },
      border: "none",
      boxShadow: "none",
    },
    children: "Import",
  };

  const addButtonProps = {
    variant: "contained" as "contained",
    children: "Tạo chiến dịch",
    onClick: () => setIsOpenModalCreate(true),
  };

  const tableContainerProps = {
    sx: {
      overflowX: "auto",
      maxWidth: "100%",
    },
  };

  const paginationBoxProps = {
    sx: {
      display: "flex",
      justifyContent: "flex-end",
      alignItems: "center",
      px: 2,
      py: 1.5,
      flexDirection: { xs: "column", sm: "row" },
      gap: 1,
    },
  };

  const handleDeleteCampaign = (campaign) => {};

  const handleChangeChannelType = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPage(0);
    const value = event.target.value as ZALO_NOTI_HISTORY_ENUM;

    const status = value === ZALO_NOTI_HISTORY_ENUM.System ? ZALO_NOTI_HISTORY_ENUM.System : value;

    setFilterData((prev) => ({
      ...prev,
      Status: status,
      Paging: { ...prev.Paging, PageIndex: SKIP },
    }));
  };

  const handleChangeTriggerEvent = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPage(0);
    const value = e.target.value;
    setFilterData((prev) => ({
      ...prev,
      TriggerEventCode: value,
      Paging: { ...prev.Paging, PageIndex: SKIP },
    }));
  };

  const currentPageStart = filterData.Paging.PageIndex * filterData.Paging.PageSize + 1;
  const currentPageEnd = Math.min(
    filterData.Paging.PageSize * (filterData.Paging.PageIndex + 1),
    total
  );
  const isPrevDisabled = filterData.Paging.PageIndex === 0;
  const isNextDisabled = currentPageEnd >= total;

  const handleChangePage = (direction: "next" | "prev") => {
    setFilterData((prev) => {
      const newPageIndex =
        direction === "next" ? prev.Paging.PageIndex + 1 : Math.max(0, prev.Paging.PageIndex - 1);
      return {
        ...prev,
        Paging: {
          PageIndex: newPageIndex,
          PageSize: prev.Paging.PageSize,
        },
      };
    });
  };

  const debounce = (func: (...args: any[]) => void, wait: number) => {
    let timeout: NodeJS.Timeout | null = null;
    return (...args: any[]) => {
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  };

  const handleSearchChange = useCallback(
    debounce((value: string) => {
      setPage(0);
      setFilterData((prev) => ({
        ...prev,
        SearchKeyword: value,
      }));
    }, 1000),
    []
  );

  const tableCellBaseStyles = {
    fontSize: { xs: "12px", md: "14px" },
  };

  const tableHeaderCellStyles = {
    ...tableCellBaseStyles,
    fontWeight: 600,
  };

  const statusBadgeStyles = {
    display: "inline-block",
    px: { xs: 1, md: 1.5 },
    py: 0.5,
    borderRadius: 1,
    fontWeight: 500,
    fontSize: { xs: 11, md: 13 },
    whiteSpace: "nowrap",
  };

  return (
    <>
      <Box sx={{ p: { xs: 1, md: 2 } }}>
        <Typography
          sx={{
            marginRight: 1,
            fontSize: { xs: 18, md: 20 },
            fontWeight: 700,
            marginBottom: 1.5,
          }}
        >
          Lịch sử thông báo
        </Typography>

        <Box
          sx={{
            display: "flex",
            alignItems: { xs: "stretch", md: "center" },
            justifyContent: "space-between",
            marginBottom: 3,
            flexDirection: { xs: "column", lg: "row" },
            gap: { xs: 2, lg: 1 },
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              flexDirection: { xs: "row", sm: "row" },
              width: { xs: "100%", lg: "auto" },
            }}
          >
            <Select
              value={filterData.Status == "" ? ZALO_HISTORY_STATUS_ENUM.All : filterData.Status}
              onChange={handleChangeChannelType}
              size="small"
              sx={{
                width: { xs: "50%", sm: 120 },
                "& .MuiSelect-select": {
                  fontSize: { xs: "14px", md: "16px" },
                },
              }}
            >
              <MenuItem value="all">Tất cả</MenuItem>
              {listStatusNotiHistory.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            <Select
              value={filterData.TriggerEventCode}
              onChange={handleChangeTriggerEvent}
              size="small"
              sx={{
                width: { xs: "50%", sm: 300 },
                "& .MuiSelect-select": {
                  fontSize: { xs: "14px", md: "16px" },
                },
              }}
            >
              <MenuItem value="all">Tất cả</MenuItem>
              {Array.isArray(listTriggerEvent) &&
                listTriggerEvent.length > 0 &&
                listTriggerEvent.map((option) => (
                  <MenuItem key={option.triggerEventId} value={option.code}>
                    {option.eventName}
                  </MenuItem>
                ))}
            </Select>
          </Box>

          <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale="vi">
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 1,
                flexDirection: { xs: "row", sm: "row" },
                width: { xs: "100%", lg: "auto" },
              }}
            >
              <DatePicker
                value={dayjs(filterData.FromDate)}
                format="DD/MM/YYYY"
                onChange={(newValue) => handleDateRangeChange([newValue, dayjs(filterData.ToDate)])}
                slotProps={{
                  textField: {
                    size: "small",
                    sx: {
                      height: 40,
                      width: { xs: "calc(50% - 8px)", sm: 200 },
                      "& .MuiInputBase-input": {
                        fontSize: { xs: "14px", md: "16px" },
                      },
                    },
                    InputProps: { sx: { height: 40 } },
                  },
                }}
              />
              <Typography
                sx={{
                  display: { xs: "block", sm: "block" }, // Hiển thị trên mobile
                  fontSize: { xs: "14px", md: "16px" },
                  minWidth: "16px", // Đảm bảo có chỗ cho dấu -
                  textAlign: "center",
                }}
              >
                -
              </Typography>
              <DatePicker
                value={dayjs(filterData.ToDate)}
                format="DD/MM/YYYY"
                onChange={(newValue) =>
                  handleDateRangeChange([dayjs(filterData.FromDate), newValue])
                }
                shouldDisableDate={(date) => date.isBefore(dayjs(filterData.FromDate), "day")}
                slotProps={{
                  textField: {
                    size: "small",
                    sx: {
                      height: 40,
                      width: { xs: "calc(50% - 8px)", sm: 200 },
                      "& .MuiInputBase-input": {
                        fontSize: { xs: "14px", md: "16px" },
                      },
                    },
                    InputProps: { sx: { height: 40 } },
                  },
                }}
              />
            </Box>
          </LocalizationProvider>

          {/* Search - Để cuối cùng */}
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              width: { xs: "100%", lg: "auto" },
            }}
          >
            <TextField
              {...searchFieldProps}
              onChange={(e) => handleSearchChange(e.target.value)}
              sx={{
                width: { xs: "100%", sm: 300 },
                "& .MuiOutlinedInput-root": {
                  borderRadius: 30,
                  backgroundColor: "#fff",
                  fontSize: { xs: "14px", md: "16px" },
                },
              }}
            />
          </Box>
        </Box>
      </Box>

      <Paper elevation={3} sx={{ borderRadius: 2, overflow: "hidden" }}>
        <Box sx={{ overflowX: "auto" }}>
          <Table stickyHeader sx={{ minWidth: 900 }}>
            <TableHead>
              <TableRow>
                <TableCell sx={{ ...tableHeaderCellStyles, width: "50px", minWidth: "50px" }}>
                  STT
                </TableCell>
                <TableCell sx={{ ...tableHeaderCellStyles, minWidth: "150px" }}>Sự kiện</TableCell>
                <TableCell sx={{ ...tableHeaderCellStyles, minWidth: "100px" }}>Kênh</TableCell>
                <TableCell sx={{ ...tableHeaderCellStyles, minWidth: "120px" }}>
                  Người nhận
                </TableCell>
                <TableCell sx={{ ...tableHeaderCellStyles, minWidth: "100px" }}>
                  Trạng thái
                </TableCell>
                <TableCell sx={{ ...tableHeaderCellStyles, minWidth: "100px" }}>Giá</TableCell>
                <TableCell sx={{ ...tableHeaderCellStyles, minWidth: "140px" }}>
                  Thời gian gửi
                </TableCell>
                <TableCell sx={{ ...tableHeaderCellStyles, minWidth: "120px" }}>Ghi chú</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.isArray(listTriggerEventHistory) && listTriggerEventHistory.length > 0 ? (
                listTriggerEventHistory.map((row, index) => {
                  return (
                    <TableRow hover role="checkbox" tabIndex={-1} key={row.triggerEventId}>
                      <TableCell component="th" scope="row" sx={tableCellBaseStyles}>
                        {filterData?.Paging?.PageIndex * filterData?.Paging?.PageSize + index + 1}
                      </TableCell>
                      <TableCell sx={{ ...tableCellBaseStyles, wordBreak: "break-word" }}>
                        {row.triggerEventName}
                      </TableCell>
                      <TableCell sx={tableCellBaseStyles}>{row.channelName}</TableCell>
                      <TableCell sx={{ ...tableCellBaseStyles, wordBreak: "break-word" }}>
                        {row.recipient}
                      </TableCell>
                      <TableCell>
                        <Box
                          sx={{
                            ...statusBadgeStyles,
                            color: getStatusConfig(row.status).color,
                            backgroundColor: getStatusConfig(row.status).backgroundColor,
                          }}
                        >
                          {row.status === "Success" ? "Thành công" : "Thất bại"}
                        </Box>
                      </TableCell>
                      <TableCell sx={{ ...tableCellBaseStyles, whiteSpace: "nowrap" }}>
                        {formatPrice(row.price)}
                        {CURRENCY_UNIT}
                      </TableCell>
                      <TableCell sx={{ ...tableCellBaseStyles, whiteSpace: "nowrap" }}>
                        {dayjs(row.sendTime).format("DD/MM/YYYY HH:mm:ss")}
                      </TableCell>
                      <TableCell
                        sx={{ ...tableCellBaseStyles, wordBreak: "break-word", maxWidth: "150px" }}
                      >
                        {row.description}
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={9} align="center" sx={{ py: 3 }}>
                    <Typography variant="body2" color="text.secondary" sx={tableCellBaseStyles}>
                      Không có dữ liệu
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </Box>
      </Paper>

      <Box
        sx={{
          display: "flex",
          justifyContent: "end",
          // justifyContent: "flex-end",
          alignItems: "center",
          px: { xs: 1, md: 2 },
          py: 1.5,
          flexDirection: "row",
          gap: 1,
          mt: 2,
          flexWrap: "wrap",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            textAlign: "right",
            alignItems: "center",
            gap: { xs: 0.5, md: 1 },
          }}
        >
          <Typography
            // variant="body2"
            sx={{
              fontSize: { xs: "11px", md: "14px" },
              whiteSpace: "nowrap",
              textAlign: "right",
            }}
          >
            Số dòng mỗi trang
          </Typography>
          <Select
            value={Number(filterData.Paging.PageSize)}
            onChange={handleChangeRowsPerPage}
            size="small"
            sx={{
              minWidth: { xs: 50, md: 60 },
              "& .MuiOutlinedInput-notchedOutline": { border: "none" },
              "& .MuiSelect-select": {
                padding: { xs: "2px 6px", md: "4px 8px" },
                fontSize: { xs: "11px", md: "14px" },
              },
            }}
          >
            <MenuItem value={10}>10</MenuItem>
            <MenuItem value={25}>25</MenuItem>
            <MenuItem value={50}>50</MenuItem>
          </Select>
        </Box>

        <Typography
          variant="body2"
          sx={{
            fontSize: { xs: "11px", md: "14px" },
            whiteSpace: "nowrap",
            textAlign: "right",
            // flex: 3,
            mx: 2,
          }}
        >
          {`${currentPageStart}-${currentPageEnd} của ${total}`}
        </Typography>

        <Box
          sx={{
            display: "flex",
            alignItems: "center",
          }}
        >
          <IconButton
            disabled={isPrevDisabled}
            onClick={() => handleChangePage("prev")}
            size="small"
            sx={{ padding: { xs: "4px", md: "8px" } }}
          >
            <NavigateBeforeIcon fontSize="small" />
          </IconButton>
          <IconButton
            disabled={isNextDisabled}
            onClick={() => handleChangePage("next")}
            size="small"
            sx={{ padding: { xs: "4px", md: "8px" } }}
          >
            <NavigateNextIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>

      <Dialog
        open={isOpenModalDelete}
        onClose={() => setIsOpenModalDelete(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            margin: { xs: 1, sm: 3 },
            maxHeight: { xs: "calc(100% - 32px)", sm: "calc(100% - 96px)" },
          },
        }}
      >
        <DialogTitle sx={{ fontSize: { xs: "18px", md: "20px" } }}>Xoá chiến dịch</DialogTitle>
        <DialogContent>
          <DialogContentText sx={{ fontSize: { xs: "14px", md: "16px" } }}>
            Bạn có chắc chắn muốn xoá chiến dịch này?
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ padding: { xs: "16px 24px 24px", md: "8px 24px 24px" } }}>
          <Button
            onClick={() => setIsOpenModalDelete(false)}
            variant="outlined"
            sx={{ fontSize: { xs: "14px", md: "16px" } }}
          >
            Huỷ
          </Button>
          <Button
            color="error"
            variant="contained"
            autoFocus
            sx={{ fontSize: { xs: "14px", md: "16px" } }}
          >
            Xoá
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default Report;
