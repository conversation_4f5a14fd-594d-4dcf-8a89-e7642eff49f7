import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import { useTranslation } from "react-i18next";
import { Box } from "@mui/material";
import { Button, Space, Tag, Image, Modal, Tabs, message } from "antd";
import { useStoreId } from "@/src/hooks/use-store-id";
import { tokens } from "@/src/locales/tokens";
import { useProductCategory } from "@/src/api/hooks/dashboard/product/use-category";
import DashboardLayout from "@/src/layouts/dashboard";
import { paths } from "@/src/paths";
import { StorageService } from "nextjs-api-lib";
import TitleTypography from "@/src/components/title-typography/title-typography";
import { Padding } from "@/src/styles/CommonStyle";
import { useAllPermissions } from "@/src/api/hooks/auth/use-all-permissions";
import { usePathname } from "next/navigation";
import { PERMISSION_TYPE_ENUM, PermissionType } from "@/src/constants/constant";
import { useDebounce } from "@/src/hooks/use-debounce";
import { GetProductCategoryRequest } from "@/src/api/types/product-category.types";
import { CategoryDto } from "@/src/api/services/dashboard/product/category.service";
import { useValidImage } from "@/src/hooks/useValidImage";
import { useAppSelector } from "@/src/redux/hooks";

// Import Ant Design Table components
import {
  CommonTable,
  useTablePagination,
  useTableSelection,
  useTableActions,
  createIndexColumn,
  createTextColumn,
  createNumberColumn,
  BaseTableColumn,
  TableRef,
  AddIcon,
} from "@/src/components/common/antd-table";

const TAB_STORAGE_KEY = "categoryManagementTab";

const CategoryManagementAntd = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const pathname = usePathname();
  const tableRef = useRef<TableRef>(null);

  const { getProductCategory, deleteProductCategory, deleteProductCategories, loading } =
    useProductCategory();

  const [categories, setCategories] = useState<CategoryDto[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [tabValue, setTabValue] = useState(0);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const debouncedSearchValue = useDebounce(searchTerm, 500);
  const storeId = useStoreId();
  const currentShop = useAppSelector((state) => state.shop.currentShop);

  const { permissions } = useAllPermissions();

  // Table hooks
  const { pagination, setTotal } = useTablePagination({
    initialPage: 1,
    initialPageSize: 10,
    onChange: (page, pageSize) => {
      fetchCategories(page - 1, pageSize);
    },
  });

  const { rowSelection, selectedRows, clearSelection } = useTableSelection<CategoryDto>({
    rowKey: "categoryId",
    getCheckboxProps: (record) => ({
      disabled: record.quantityItems > 0,
    }),
  });

  const isGranted = (url: string, permission: PermissionType) => {
    if (url.endsWith("/")) {
      url = url.slice(0, -1);
    }
    const matchingUrl = Object.keys(permissions).find((key) => key === url);
    if (!matchingUrl) return false;
    return permissions[matchingUrl]?.includes(permission) || false;
  };

  // Table actions
  const actions = useTableActions<CategoryDto>({
    onEdit: handleEdit,
    onDelete: handleDelete,
    canEdit: () => isGranted(pathname, PERMISSION_TYPE_ENUM.Edit),
    canDelete: (record) =>
      isGranted(pathname, PERMISSION_TYPE_ENUM.Delete) && record.quantityItems === 0,
  });

  // Table columns
  const columns: BaseTableColumn<CategoryDto>[] = [
    createIndexColumn("STT", { width: 60 }),
    {
      key: "image",
      title: t(tokens.contentManagement.category.table.image),
      width: 80,
      align: "center",
      render: (_, record) => {
        const imgSrc = useValidImage(record?.image?.link, currentShop?.shopLogo?.link);
        return (
          <Image
            src={imgSrc}
            alt={record.categoryName}
            width={40}
            height={40}
            style={{ objectFit: "cover", borderRadius: 4 }}
            preview={!!imgSrc}
          />
        );
      },
    },
    createTextColumn<CategoryDto>(
      "categoryName",
      t(tokens.contentManagement.category.table.name),
      "categoryName",
      { ellipsis: true, maxLength: 50, tooltip: true }
    ),
    createTextColumn<CategoryDto>(
      "parentName",
      t(tokens.contentManagement.category.table.parentCategory),
      "parentName",
      { ellipsis: true }
    ),
    createNumberColumn<CategoryDto>(
      "quantityItems",
      t(tokens.contentManagement.category.table.productCount),
      "quantityItems",
      { width: 120 }
    ),
    createNumberColumn<CategoryDto>(
      "categoryPosition",
      t(tokens.contentManagement.category.table.displayOrder),
      "categoryPosition",
      { width: 120 }
    ),
    {
      key: "publish",
      title: t(tokens.contentManagement.category.table.status),
      dataIndex: "publish",
      width: 120,
      align: "center",
      render: (status: string) => {
        const statusMap = {
          Actived: { color: "success", text: "Hoạt động" },
          InActived: { color: "default", text: "Không hoạt động" },
          Publish: { color: "success", text: "Đã xuất bản" },
          UnPublish: { color: "default", text: "Chưa xuất bản" },
        };
        const config = statusMap[status as keyof typeof statusMap] || {
          color: "default",
          text: status,
        };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
  ];

  useEffect(() => {
    const { tab } = router.query;
    if (tab) {
      const index = parseInt(tab as string, 10);
      if (!isNaN(index) && index >= 0 && index <= 1) {
        setTabValue(index);
        localStorage.setItem(TAB_STORAGE_KEY, index.toString());
      }
    }
  }, [router.query]);

  useEffect(() => {
    if (storeId) {
      setIsInitialLoading(true);
      fetchCategories().finally(() => {
        setIsInitialLoading(false);
      });
    }
  }, [debouncedSearchValue, storeId, tabValue]);

  const fetchCategories = async (page = 0, pageSize = 10) => {
    try {
      const categoryType = tabValue === 0 ? "Product" : "Service";
      const params: GetProductCategoryRequest = {
        categoryType,
        shopId: storeId,
        partnerId: StorageService.get("partnerId") as string,
        paging: {
          pageIndex: page,
          pageSize: pageSize,
          search: debouncedSearchValue,
          nameType: "Created",
          sortType: "Desc",
          name: "Created",
          sort: "Desc",
        },
      };
      const response = await getProductCategory(params);
      setCategories(response?.data?.data || []);
      setTotal(response?.data?.total || 0);
    } catch (error) {
      message.error("Lỗi khi tải dữ liệu");
    }
  };

  const handleTabChange = (key: string) => {
    const newValue = parseInt(key);
    setTabValue(newValue);
    clearSelection();
    localStorage.setItem(TAB_STORAGE_KEY, newValue.toString());
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, tab: newValue },
      },
      undefined,
      { shallow: true }
    );
  };

  const handleCreate = () => {
    const categoryType = tabValue === 0 ? "Product" : "Service";
    StorageService.set("categoryType", categoryType);
    router.push(paths.dashboard.product.createProductCategory);
  };

  function handleEdit(category: CategoryDto) {
    const categoryType = tabValue === 0 ? "Product" : "Service";
    StorageService.set("categoryType", categoryType);
    StorageService.set("editCategoryData", category);
    router.push({
      pathname: paths.dashboard.product.createProductCategory,
      query: { id: category.categoryId },
    });
  }

  function handleDelete(category: CategoryDto) {
    Modal.confirm({
      title: "Xác nhận xóa",
      content: `Bạn có chắc chắn muốn xóa danh mục "${category.categoryName}" không?`,
      okText: "Xóa",
      cancelText: "Hủy",
      okType: "danger",
      onOk: async () => {
        try {
          if (category.quantityItems === 0) {
            const res = await deleteProductCategory(category.categoryId);
            if (res?.status === 200) {
              message.success("Xóa danh mục thành công");
              fetchCategories();
            }
          } else {
            message.warning("Danh mục đang được sử dụng trong sản phẩm, không thể xóa");
          }
        } catch (error) {
          message.error("Xóa danh mục thất bại");
        }
      },
    });
  }

  const handleDeleteMultiple = () => {
    if (selectedRows.length === 0) return;

    Modal.confirm({
      title: "Xác nhận xóa nhiều danh mục",
      content: `Bạn có chắc chắn muốn xóa ${selectedRows.length} danh mục đã chọn không?`,
      okText: "Xóa",
      cancelText: "Hủy",
      okType: "danger",
      onOk: async () => {
        try {
          if (storeId) {
            const selectedIds = selectedRows.map((row) => row.categoryId);
            const res = await deleteProductCategories(storeId, selectedIds);
            if (res?.status === 200) {
              message.success(`Xóa ${selectedRows.length} danh mục thành công`);
              clearSelection();
              fetchCategories();
            }
          }
        } catch (error) {
          message.error("Xóa danh mục thất bại");
        }
      },
    });
  };

  const getTitleByTab = (tabIndex: number) => {
    return tabIndex === 0
      ? "Hàng hoá/Danh mục/Danh mục sản phẩm"
      : "Hàng hoá/Danh mục/Danh mục dịch vụ";
  };

  const tabItems = [
    {
      key: "0",
      label: t(tokens.contentManagement.category.tabs.product),
    },
    // Uncomment if service tab is needed
    // {
    //   key: '1',
    //   label: t(tokens.contentManagement.category.tabs.service),
    // },
  ];

  return (
    <DashboardLayout>
      <Box sx={{ p: Padding, paddingTop: "20px" }}>
        <TitleTypography
          sx={{
            fontSize: "20px !important",
            lineHeight: "1",
            fontWeight: "700",
            paddingBottom: "20px",
            marginBottom: "16px",
            borderBottom: "1px solid #bdbdbd",
          }}
        >
          {getTitleByTab(tabValue)}
        </TitleTypography>

        <Tabs
          activeKey={tabValue.toString()}
          onChange={handleTabChange}
          items={tabItems}
          style={{ marginBottom: 16 }}
        />

        <CommonTable
          ref={tableRef}
          columns={columns}
          data={categories}
          loading={loading || isInitialLoading}
          pagination={pagination}
          rowSelection={rowSelection}
          actions={actions}
          searchable
          searchPlaceholder={t(tokens.contentManagement.category.search.placeholder)}
          onSearch={setSearchTerm}
          toolbar={{
            title: "",
            extra: (
              <Space>
                {selectedRows.length > 0 && (
                  <Button
                    danger
                    onClick={handleDeleteMultiple}
                    disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Delete)}
                  >
                    Xóa đã chọn ({selectedRows.length})
                  </Button>
                )}
                <Button
                  type="primary"
                  icon={<AddIcon />}
                  onClick={handleCreate}
                  disabled={!isGranted(pathname, PERMISSION_TYPE_ENUM.Add)}
                >
                  {t(tokens.contentManagement.category.actions.create)}
                </Button>
              </Space>
            ),
            showRefresh: true,
            onRefresh: fetchCategories,
          }}
          emptyText="Không có dữ liệu"
          emptyDescription="Chưa có danh mục nào được tạo"
          emptyAction={{
            text: t(tokens.contentManagement.category.actions.create),
            onClick: handleCreate,
          }}
          rowKey="categoryId"
          size="middle"
          bordered
        />
      </Box>
    </DashboardLayout>
  );
};

export default CategoryManagementAntd;
