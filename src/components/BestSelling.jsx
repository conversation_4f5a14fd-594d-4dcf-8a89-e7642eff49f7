import React, { useState } from 'react';
import {
  Box,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Typography,
  Select,
  MenuItem,
  FormControl,
  Avatar,
} from '@mui/material';
import { formatPrice } from '../api/types/membership.types';
import { CURRENCY_UNIT } from '../constants/constant';

const bestSellingData = [
  {
    product: 'Apple Watch',
    image: '/path-to-apple-watch.jpg',
    stock: 1002,
    sold: 1002,
    revenue: '23,234,295',
  },
  {
    product: 'Apple Watch',
    image: '/path-to-apple-watch.jpg',
    stock: 1002,
    sold: 423,
    revenue: '23,234,295',
  },
  {
    product: 'Apple Watch',
    image: '/path-to-apple-watch.jpg',
    stock: 1002,
    sold: 423,
    revenue: '23,234,295',
  },
];

const BestSelling = ({ selectedMonth, setSelectedMonth, handleMonthChange, top10Items }) => {
  return (
    <Box
      sx={{
        p: 3,
        boxShadow: '6px 6px 54px 0 #0000000D !important;',
        borderRadius: '15px',
        marginTop: '35px',
        background: '#fff',
        '@media(max-width: 600px)': {
          padding: '15px',
        },
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Top 10 sản phẩm bán chạy</Typography>
        <FormControl sx={{ minWidth: 120, border: '1px solid #D5D5D5', borderRadius: '12px' }}>
          <Select
            sx={{ height: '36px' }}
            value={selectedMonth}
            onChange={handleMonthChange}
            displayEmpty
          >
            <MenuItem value="1">Tháng 1</MenuItem>
            <MenuItem value="2">Tháng 2</MenuItem>
            <MenuItem value="3">Tháng 3</MenuItem>
            <MenuItem value="4">Tháng 4</MenuItem>
            <MenuItem value="5">Tháng 5</MenuItem>
            <MenuItem value="6">Tháng 6</MenuItem>
            <MenuItem value="7">Tháng 7</MenuItem>
            <MenuItem value="8">Tháng 8</MenuItem>
            <MenuItem value="9">Tháng 9</MenuItem>
            <MenuItem value="10">Tháng 10</MenuItem>
            <MenuItem value="11">Tháng 11</MenuItem>
            <MenuItem value="12">Tháng 12</MenuItem>
          </Select>
        </FormControl>
      </Box>

      <Table sx={{ backgroundColor: '#fff', borderRadius: '8px' }}>
        <TableHead sx={{ background: '#F1F4F9', borderRadius: '12px' }}>
          <TableRow sx={{ borderRadius: '12px' }}>
            <TableCell
              sx={{
                background: '#F1F4F9 !important',
                '@media(max-width: 480px)': {
                  fontSize: '14px',
                },
              }}
            >
              STT
            </TableCell>
            <TableCell
              sx={{
                background: '#F1F4F9 !important',
                '@media(max-width: 480px)': {
                  fontSize: '14px',
                },
              }}
            >
              Sản phẩm
            </TableCell>
            {/* <TableCell
              sx={{
                background: '#F1F4F9 !important',
                '@media(max-width: 480px)': {
                  fontSize: '14px',
                },
              }}
            >
              Tồn kho
            </TableCell> */}
            <TableCell
              sx={{
                background: '#F1F4F9 !important',
                '@media(max-width: 480px)': {
                  fontSize: '14px',
                },
              }}
            >
              Số lượng bán
            </TableCell>
            <TableCell
              sx={{
                background: '#F1F4F9 !important',
                '@media(max-width: 480px)': {
                  fontSize: '14px',
                },
              }}
            >
              Doanh thu
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {top10Items.length > 0 ? (
            top10Items.map((item, index) => (
              <TableRow key={index}>
                <TableCell
                  sx={{
                    '@media(max-width: 480px)': {
                      fontSize: '14px',
                      padding: '10px',
                    },
                  }}
                >
                  {index + 1}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Avatar
                      src={item.image}
                      alt={item.name}
                      sx={{
                        width: 40,
                        height: 40,
                        '@media(max-width: 480px)': {
                          height: '25px',
                          width: '25px',
                        },
                      }}
                    />
                    <Typography
                      sx={{
                        '@media(max-width: 480px)': {
                          fontSize: '14px',
                        },
                      }}
                    >
                      {item.name}
                    </Typography>
                  </Box>
                </TableCell>
                {/* <TableCell
                  sx={{
                    '@media(max-width: 480px)': {
                      fontSize: '14px',
                      padding: '10px',
                    },
                  }}
                >
                  {item.stock}
                </TableCell> */}
                <TableCell
                  sx={{
                    '@media(max-width: 480px)': {
                      fontSize: '14px',
                      padding: '10px',
                    },
                  }}
                >
                  {item.totalQuantity}
                </TableCell>
                <TableCell
                  sx={{
                    '@media(max-width: 480px)': {
                      fontSize: '14px',
                      padding: '10px',
                    },
                  }}
                >
                  {formatPrice(item.totalRevenue)}{CURRENCY_UNIT}
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow >
              <TableCell sx={{ paddingTop: 10, fontSize: 16, fontWeight: 400 }} colSpan={14} align="center">
                Không có dữ liệu
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </Box>
  );
};

export default BestSelling;
