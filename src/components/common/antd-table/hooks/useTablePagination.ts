import { useState, useCallback } from 'react';

interface UseTablePaginationProps {
  initialPage?: number;
  initialPageSize?: number;
  total?: number;
  onChange?: (page: number, pageSize: number) => void;
}

interface UseTablePaginationReturn {
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger: boolean;
    showQuickJumper: boolean;
    showTotal: (total: number, range: [number, number]) => string;
    pageSizeOptions: string[];
    onChange: (page: number, pageSize: number) => void;
  };
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  setTotal: (total: number) => void;
  reset: () => void;
}

export const useTablePagination = ({
  initialPage = 1,
  initialPageSize = 10,
  total = 0,
  onChange,
}: UseTablePaginationProps = {}): UseTablePaginationReturn => {
  const [current, setCurrent] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [totalCount, setTotalCount] = useState(total);

  const handleChange = useCallback((page: number, size: number) => {
    setCurrent(page);
    setPageSize(size);
    onChange?.(page, size);
  }, [onChange]);

  const setPage = useCallback((page: number) => {
    setCurrent(page);
    onChange?.(page, pageSize);
  }, [pageSize, onChange]);

  const setPageSizeValue = useCallback((size: number) => {
    setPageSize(size);
    setCurrent(1); // Reset to first page when changing page size
    onChange?.(1, size);
  }, [onChange]);

  const setTotal = useCallback((total: number) => {
    setTotalCount(total);
  }, []);

  const reset = useCallback(() => {
    setCurrent(initialPage);
    setPageSize(initialPageSize);
    setTotalCount(0);
  }, [initialPage, initialPageSize]);

  const pagination = {
    current,
    pageSize,
    total: totalCount,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => 
      `${range[0]}-${range[1]} của ${total} mục`,
    pageSizeOptions: ['10', '20', '50', '100'],
    onChange: handleChange,
  };

  return {
    pagination,
    setPage,
    setPageSize: setPageSizeValue,
    setTotal,
    reset,
  };
};
