export const SKIP = 0;
export const LIMIT = 10;
export const ACTIVE_STATUS = "Active";
export const INACTIVE_STATUS = "Inactive";
export const TEXT_ALIGN_LEFT = "left";
export const TEXT_ALIGN_CENTER = "center";
export const TEXT_ALIGN_RIGHT = "right";
export const SUCCESS_STATUS = "Success";

export const ZALO_TABS = [
  { value: 0, text: "TransactionNotification", label: "Thông báo giao dịch" },
  { value: 1, text: "CustomerCare", label: "Chăm sóc khách hàng" },
  { value: 2, text: "AfterSalesCommunication", label: "Hậu mãi, truyền thông" },
  { value: 3, text: "PartnerNotification", label: "Thông báo đối tác" },
];

export const PUSH_NOTIFICATION_TABS = [
  { value: 0, text: "TransactionNotification", label: "Thông báo giao dịch" },
  { value: 1, text: "CustomerCare", label: "Chăm sóc khách hàng" },
  { value: 2, text: "AfterSalesCommunication", label: "Hậu mãi, truyền thông" },
  { value: 3, text: "PartnerNotification", label: "Thông báo đối tác" },
];
export const TEMPLATE_TYPE = [
  { value: 0, text: "Transaction", label: "Tin giao dịch" },
  { value: 1, text: "Promotion", label: "Tin truyền thông" },
  { value: 2, text: "Consultation", label: "Tin văn bản" },
];

export enum ZALO_HISTORY_STATUS_ENUM {
  All = "All",
  Processing = "Processing",
  Success = "Success",
  Failed = "Failed",
  Cancelled = "Cancelled",
  SentButFailed = "SentButFailed",
}

export enum ZALO_NOTI_HISTORY_ENUM {
  // All = "",
  System = "System",
  UID = "UID",
  // ZNS = "ZNS",
  // Email = "Email",
  // SMS = "SMS",
}

export const statusOptions = [
  { value: ZALO_HISTORY_STATUS_ENUM.All, label: "Tất cả" },
  { value: ZALO_HISTORY_STATUS_ENUM.Processing, label: "Đang xử lý" },
  { value: ZALO_HISTORY_STATUS_ENUM.Success, label: "Thành công" },
  { value: ZALO_HISTORY_STATUS_ENUM.Failed, label: "Thất bại" },
  { value: ZALO_HISTORY_STATUS_ENUM.Cancelled, label: "Đã hủy" },
  { value: ZALO_HISTORY_STATUS_ENUM.SentButFailed, label: "Gửi nhưng thất bại" },
];

export const listStatusNotiHistory = [
  // { value: ZALO_NOTI_HISTORY_ENUM.All, label: "Tất cả" },
  { value: ZALO_NOTI_HISTORY_ENUM.System, label: "Hệ thống" },
  { value: ZALO_NOTI_HISTORY_ENUM.UID, label: "Zalo UID" },
  // { value: ZALO_NOTI_HISTORY_ENUM.ZNS, label: "ZNS" },
  // { value: ZALO_NOTI_HISTORY_ENUM.Email, label: "Email" },
  // { value: ZALO_NOTI_HISTORY_ENUM.SMS, label: "SMS" },
];
export enum PERMISSION_TYPE_ENUM {
  View = "View",
  Add = "Add",
  Edit = "Edit",
  Delete = "Delete",
  Import = "Import",
  Export = "Export",
}

export type PermissionType = "View" | "Add" | "Edit" | "Delete" | "Import" | "Export";

export const IMAGE_TYPE = [".png", ".jpg", ".jpeg"];
// export const FILE_SIZE_2MB = 2 * 1024 * 1024; //2MB
export const FILE_SIZE_2MB = 2 * 1024 * 1024; //2MB
export const FILE_SIZE_5MB = 5 * 1024 * 1024; //5MB
export const FILE_SIZE_25MB = 25 * 1024 * 1024; //25MB

export const MAX_FILE_IMAGE = 20;
export const EXCEL_MAX_SIZE = 10 * 1024 * 1024; // 10MB

export const PrizeType = {
  Digital: "PRIZE_TYPE_DIGITAL",
  Ticket: "PRIZE_TYPE_TICKET",
  Physical: "PRIZE_TYPE_PHYSICAL",
};

export const PRIZE_TYPES = [
  { value: PrizeType.Ticket, label: "Lượt chơi" },
  { value: PrizeType.Physical, label: "Quà tặng" },
];

export const enum PRIZE_CATEGORY {
  Voucher = "Voucher",
  Gift = "Gift",
  Product = "Product",
  Other = "Other",
}

export const CURRENCY_UNIT = "đ";
