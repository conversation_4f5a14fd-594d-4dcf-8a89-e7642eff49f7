import React from "react";
import { Chip } from "@mui/material";
import { SyncServiceEnum } from "@/src/types/product/form";

interface ExternalSourceChipProps {
  externalSource?: string;
  size?: "small" | "medium";
}

const getExternalSourceName = (externalSource: string | null): string => {
  console.log(externalSource);

  if (!externalSource) return "Nội bộ";

  switch (externalSource) {
    case SyncServiceEnum.Sapo:
      return SyncServiceEnum.Sapo;
    case SyncServiceEnum.KiotViet:
      return SyncServiceEnum.KiotViet;
    case SyncServiceEnum.NhanhVN:
      return SyncServiceEnum.NhanhVN;
    case SyncServiceEnum.Odoo:
      return SyncServiceEnum.Odoo;
    default:
      return "Nội bộ";
  }
};

const getExternalSourceColor = (
  externalSource: string | null
): "primary" | "secondary" | "success" | "warning" | "error" | "info" | "default" => {
  if (!externalSource) return "default";

  switch (externalSource) {
    case SyncServiceEnum.Sapo:
      return "primary";
    case SyncServiceEnum.KiotViet:
      return "success";
    case SyncServiceEnum.NhanhVN:
      return "warning";
    case SyncServiceEnum.Odoo:
      return "info";
    default:
      return "default";
  }
};

export const ExternalSourceChip: React.FC<ExternalSourceChipProps> = ({
  externalSource,
  size = "small",
}) => {
  return (
    <Chip
      label={getExternalSourceName(externalSource)}
      color={getExternalSourceColor(externalSource)}
      size={size}
      variant={externalSource ? "filled" : "outlined"}
    />
  );
};

export default ExternalSourceChip;
